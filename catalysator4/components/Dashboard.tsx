
import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useProjects } from '../hooks/useProjects';
import ProjectCard from './ProjectCard';
import FileUpload from './FileUpload';
import LoadingSpinner from './LoadingSpinner';
import { ICONS } from '../constants';
import { ProjectFile, PortfolioAnalysis } from '../types';

const Dashboard: React.FC = () => {
  const { projects, addProject, isLoading, portfolioAnalysis, isLoadingPortfolio, analyzePortfolio } = useProjects();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newProjectName, setNewProjectName] = useState('');
  const [newProjectDescription, setNewProjectDescription] = useState('');
  const [newProjectFiles, setNewProjectFiles] = useState<ProjectFile[]>([]);

  useEffect(() => {
    if (projects.length > 0) { // Analyze portfolio if projects exist
      analyzePortfolio();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projects.length]); // Rerun if number of projects changes

  const handleCreateProject = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newProjectName.trim() || !newProjectDescription.trim()) {
      alert("Project name and description are required.");
      return;
    }
    const newProjectId = await addProject(newProjectName, newProjectDescription, newProjectFiles);
    if (newProjectId) {
      setNewProjectName('');
      setNewProjectDescription('');
      setNewProjectFiles([]);
      setShowCreateModal(false);
      // Optionally navigate to the new project: navigate(`/project/${newProjectId}`);
    }
  };
  
  const renderPortfolioAnalysis = (analysis: PortfolioAnalysis | null) => {
    if (!analysis) return <p className="text-neutral/70">Portfolio analysis not yet available.</p>;
    
    const hasSynergies = analysis.synergies && analysis.synergies.length > 0;
    const hasConflicts = analysis.conflicts && analysis.conflicts.length > 0;
    const hasSharedResources = analysis.sharedResources && analysis.sharedResources.length > 0;

    if (!hasSynergies && !hasConflicts && !hasSharedResources) {
      // Check if there's a specific error message in conflicts (common pattern for API failure display)
      if (hasConflicts && analysis.conflicts[0]?.description.toLowerCase().includes('failed')) {
         return <p className="text-error">{analysis.conflicts[0].description}</p>;
      }
      return <p className="text-neutral/70">No significant portfolio-level interactions detected by AI, or analysis is pending.</p>;
    }

    return (
      <div className="space-y-3">
        {hasSynergies && (
          <div>
            <h3 className="text-md font-semibold text-success">Potential Synergies:</h3>
            <ul className="list-disc list-inside text-sm text-neutral/90">
              {analysis.synergies.map((s, i) => <li key={`syn-${i}`}>{s.description} (Projects: {s.projectIds.join(', ')})</li>)}
            </ul>
          </div>
        )}
        {hasConflicts && (
          <div>
            <h3 className="text-md font-semibold text-error">Potential Conflicts:</h3>
            <ul className="list-disc list-inside text-sm text-neutral/90">
              {analysis.conflicts.map((c, i) => <li key={`con-${i}`}>{c.description}  (Projects: {c.projectIds.join(', ')})</li>)}
            </ul>
          </div>
        )}
        {hasSharedResources && (
          <div>
            <h3 className="text-md font-semibold text-info">Shared Resources Opportunities:</h3>
             <ul className="list-disc list-inside text-sm text-neutral/90">
              {analysis.sharedResources.map((r, i) => <li key={`res-${i}`}>{r.resource}: {r.description} (Projects: {r.projectIds.join(', ')})</li>)}
            </ul>
          </div>
        )}
      </div>
    );
  };


  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-neutral">Projects Dashboard</h1>
        <button
          onClick={() => setShowCreateModal(true)}
          className="btn-primary inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
        >
          {ICONS.ADD}
          <span className="ml-2">New Project</span>
        </button>
      </div>

      {isLoading && <LoadingSpinner message="Processing new project..." />}

      {projects.length === 0 && !isLoading && (
        <div className="text-center py-10 bg-base-100 rounded-lg shadow">
          <span className="text-5xl text-neutral/30">{ICONS.PROJECT}</span>
          <p className="mt-4 text-xl text-neutral/70">No projects yet.</p>
          <p className="text-neutral/60">Click "New Project" to get started with AI-powered insights!</p>
        </div>
      )}

      {projects.length > 0 && (
         <div className="bg-base-100 p-6 rounded-lg shadow-lg mb-8">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-neutral flex items-center">
              {ICONS.AI_SPARKLE} <span className="ml-2">AI Portfolio Overview</span>
            </h2>
            <button 
              onClick={analyzePortfolio} 
              disabled={isLoadingPortfolio}
              className="text-sm text-primary hover:underline disabled:opacity-50"
            >
              {isLoadingPortfolio ? 'Analyzing...' : 'Refresh Analysis'}
            </button>
          </div>
          {isLoadingPortfolio ? <LoadingSpinner message="Analyzing portfolio..." /> : renderPortfolioAnalysis(portfolioAnalysis)}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {projects.sort((a,b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()).map(project => (
          <ProjectCard key={project.id} project={project} />
        ))}
      </div>

      {showCreateModal && (
        <div className="fixed inset-0 bg-neutral/30 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-base-100 p-6 sm:p-8 rounded-lg shadow-2xl w-full max-w-lg max-h-[90vh] overflow-y-auto">
            <h2 className="text-2xl font-semibold text-neutral mb-6">Create New Project</h2>
            <form onSubmit={handleCreateProject} className="space-y-4">
              <div>
                <label htmlFor="projectName" className="block text-sm font-medium text-neutral/80">Project Name</label>
                <input
                  type="text"
                  id="projectName"
                  value={newProjectName}
                  onChange={(e) => setNewProjectName(e.target.value)}
                  className="mt-1 block w-full px-3 py-2 border border-neutral/30 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm"
                  required
                />
              </div>
              <div>
                <label htmlFor="projectDescription" className="block text-sm font-medium text-neutral/80">Project Description</label>
                <textarea
                  id="projectDescription"
                  value={newProjectDescription}
                  onChange={(e) => setNewProjectDescription(e.target.value)}
                  rows={3}
                  className="mt-1 block w-full px-3 py-2 border border-neutral/30 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-neutral/80 mb-1">Attach Files (Conceptual)</label>
                <FileUpload onFilesUploaded={setNewProjectFiles} />
              </div>
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setShowCreateModal(false)}
                  className="px-4 py-2 text-sm font-medium text-neutral/80 bg-secondary hover:bg-neutral/10 rounded-md border border-neutral/30"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isLoading}
                  className="btn-primary inline-flex items-center px-4 py-2 text-sm font-medium rounded-md shadow-sm disabled:opacity-50"
                >
                  {isLoading ? <LoadingSpinner size="sm" /> : <>{ICONS.AI_SPARKLE} <span className="ml-2">Create & Analyze</span></>}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default Dashboard;
