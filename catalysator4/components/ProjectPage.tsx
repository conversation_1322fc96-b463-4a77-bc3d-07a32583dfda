
import React, { useEffect, useState } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useProjects } from '../hooks/useProjects';
import { Project, ProjectTemplate, AISuggestion } from '../types';
import LoadingSpinner from './LoadingSpinner';
import { ICONS } from '../constants';
import KnowledgeGraphVisualizer from './KnowledgeGraphVisualizer';
import AISuggestionCard from './AISuggestionCard';

const ProjectPage: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const { getProjectById, runDeepAnalysis, isLoading, updateProjectDataCore, setSelectedTemplate: setProjectTemplate } = useProjects();
  
  const [project, setProject] = useState<Project | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  useEffect(() => {
    if (projectId) {
      const currentProject = getProjectById(projectId);
      if (currentProject) {
        setProject(currentProject);
        // Sync local analyzing state with global project status
        setIsAnalyzing(currentProject.status === 'analyzing_deep' || currentProject.status === 'analyzing_initial');
      } else {
        navigate('/'); // Project not found
      }
    }
  }, [projectId, getProjectById, navigate]);

  // This effect listens to changes in the project object from the context
  useEffect(() => {
    if (projectId) {
        const updatedProject = getProjectById(projectId);
        if (updatedProject) {
            setProject(updatedProject);
            setIsAnalyzing(updatedProject.status === 'analyzing_deep' || updatedProject.status === 'analyzing_initial');
        }
    }
  }, [projectId, getProjectById]); // Corrected dependency array

  const handleRunDeepAnalysis = async () => {
    if (project) {
      // setIsAnalyzing(true); // State update is now driven by project.status from context
      await runDeepAnalysis(project.id);
    }
  };

  const handleSaveChanges = () => {
    if (project) {
      // In a real app, this would persist to a backend. Here, it's mainly for dataCore or template changes.
      // For this example, dataCore is updated via updateProjectDataCore.
      alert('Project data (conceptually) saved!');
      console.log("Updated project data core:", project.dataCore);
    }
  };

  const handleTemplateChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    if (project) {
      const newTemplate = e.target.value as ProjectTemplate;
      setProjectTemplate(project.id, newTemplate);
      // The project state will update via useEffect listening to getProjectById
    }
  };

  if (!project && isLoading) { // Show loading spinner if project is null AND global loading is true (e.g. initial load)
    return <LoadingSpinner message="Loading project details..." />;
  }
  if (!project) { // If still no project after loading check, or if not loading but project is null (e.g. navigated to bad ID)
     return <div className="text-center p-8">Project not found or still loading. <Link to="/" className="text-primary hover:underline">Go to Dashboard</Link></div>;
  }


  return (
    <div className="space-y-8">
      <div className="bg-base-100 p-6 sm:p-8 rounded-lg shadow-xl">
        <div className="flex flex-col md:flex-row justify-between md:items-center mb-6 pb-6 border-b border-neutral/20">
          <div>
            <h1 className="text-3xl font-bold text-primary mb-1">{project.name}</h1>
            <p className="text-neutral/70">{project.description}</p>
            <p className="text-xs text-neutral/50 mt-1">
              Created: {new Date(project.createdAt).toLocaleDateString()} | Last Updated: {new Date(project.updatedAt).toLocaleDateString()}
            </p>
          </div>
          <div className="mt-4 md:mt-0">
             <button
              onClick={handleSaveChanges}
              className="btn-primary inline-flex items-center px-4 py-2 text-sm font-medium rounded-md shadow-sm mr-3"
            >
              {ICONS.SAVE} <span className="ml-2">Save Changes</span>
            </button>
          </div>
        </div>

        {/* AI Strategic Center */}
        <section className="mb-8 p-6 bg-gradient-to-br from-primary/5 to-accent/5 rounded-lg border border-primary/20">
          <h2 className="text-2xl font-semibold text-neutral mb-4 flex items-center">
            {ICONS.AI_SPARKLE} <span className="ml-2">AI Strategic Center</span>
          </h2>
          <p className="text-neutral/70 mb-4">
            Unlock deeper insights and strategic advantages. Our AI will perform a comprehensive semantic analysis of your project data to generate valuable outputs.
          </p>
          <button
            onClick={handleRunDeepAnalysis}
            disabled={isAnalyzing || isLoading} // isLoading refers to global loading from useProjects
            className="bg-accent hover:bg-accent/80 text-base-100 font-semibold inline-flex items-center px-6 py-3 text-sm rounded-md shadow-md disabled:opacity-60 transition-colors"
          >
            {isAnalyzing || project.status === 'analyzing_deep' || project.status === 'analyzing_initial' ? (
              <>
                <LoadingSpinner size="sm" color="text-base-100" />
                <span className="ml-2">AI Analyzing...</span>
              </>
            ) : (
              <>
                {ICONS.LIGHTBULB} <span className="ml-2">Run Deep Semantic Analysis & Generate Insights</span>
              </>
            )}
          </button>
          {project.status === 'error' && <p className="text-error mt-2 text-sm">Error during analysis: {project.errorDetails}</p>}
        </section>

        {/* Display AI Generated Content */}
        {project.strategicOutput && (
          <div className="space-y-8">
            {/* Project Narrative */}
            <section>
              <h3 className="text-xl font-semibold text-neutral mb-3 flex items-center">
                {ICONS.PROJECT} <span className="ml-2">AI-Generated Project Narrative</span>
              </h3>
              <div className="prose prose-sm max-w-none p-4 bg-base-100 border border-neutral/10 rounded-md shadow">
                {project.strategicOutput.projectPageNarrative.split('\n').map((paragraph, index) => (
                  <p key={index}>{paragraph}</p>
                ))}
              </div>
            </section>

            {/* Generated Image */}
            {project.strategicOutput.generatedImageUrl && (
              <section>
                <h3 className="text-xl font-semibold text-neutral mb-3 flex items-center">
                  {ICONS.IMAGE} <span className="ml-2">AI-Generated Project Image</span>
                </h3>
                <div className="bg-base-100 border border-neutral/10 rounded-md shadow overflow-hidden">
                  <img src={project.strategicOutput.generatedImageUrl} alt="AI-generated representation of the project" className="w-full h-auto max-h-96 object-contain"/>
                </div>
              </section>
            )}

            {/* Knowledge Graph */}
            <section>
              <h3 className="text-xl font-semibold text-neutral mb-3 flex items-center">
                 {ICONS.GRAPH} <span className="ml-2">AI-Generated Knowledge Graph Insights</span>
              </h3>
              <div className="p-4 bg-base-100 border border-neutral/10 rounded-md shadow">
                <KnowledgeGraphVisualizer graph={project.strategicOutput.knowledgeGraph} />
              </div>
            </section>

            {/* Strategic Suggestions */}
            <section>
              <h3 className="text-xl font-semibold text-neutral mb-3 flex items-center">
                {ICONS.LIGHTBULB} <span className="ml-2">AI Strategic Consultant</span>
              </h3>
              {project.strategicOutput.strategicSuggestions.length > 0 ? (
                <div className="space-y-4">
                  {project.strategicOutput.strategicSuggestions.map((suggestion: AISuggestion) => (
                    <AISuggestionCard key={suggestion.id} suggestion={suggestion} />
                  ))}
                </div>
              ) : (
                <p className="text-neutral/60">No specific strategic suggestions generated by AI at this time.</p>
              )}
            </section>

             {/* Template Selection */}
            <section>
              <h3 className="text-xl font-semibold text-neutral mb-3">Display Template</h3>
               <div className="max-w-xs">
                <label htmlFor="templateSelect" className="block text-sm font-medium text-neutral/80 mb-1">
                  AI Suggested: <span className="font-semibold text-primary">{project.strategicOutput.suggestedTemplate}</span>
                </label>
                <select
                  id="templateSelect"
                  value={project.selectedTemplate}
                  onChange={handleTemplateChange}
                  className="block w-full pl-3 pr-10 py-2 text-base border-neutral/30 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md shadow-sm"
                >
                  {Object.values(ProjectTemplate).map(template => (
                    <option key={template} value={template}>{template}</option>
                  ))}
                </select>
                <p className="text-xs text-neutral/60 mt-1">Note: Template selection is conceptual in this demo. Actual content rendering based on templates would require more complex logic.</p>
              </div>
            </section>
          </div>
        )}
        {!project.strategicOutput && project.status === 'analyzed_initial' && (
          <div className="text-center py-8 text-neutral/60">
            <p>Run "Deep Semantic Analysis" to generate project narrative, image, knowledge graph, and strategic suggestions.</p>
          </div>
        )}
      </div>

      {/* Files (Conceptual) */}
      <div className="bg-base-100 p-6 rounded-lg shadow-xl">
        <h2 className="text-xl font-semibold text-neutral mb-3">Project Files</h2>
        {project.files.length > 0 ? (
          <ul className="list-disc list-inside text-neutral/80 space-y-1">
            {project.files.map(file => (
              <li key={file.id} className="text-sm">{file.name} ({file.type}, {Math.round(file.size / 1024)} KB)</li>
            ))}
          </ul>
        ) : (
          <p className="text-neutral/60 text-sm">No files associated with this project.</p>
        )}
      </div>
    </div>
  );
};

export default ProjectPage;
