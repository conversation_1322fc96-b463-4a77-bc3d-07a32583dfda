
import React from 'react';
import { KnowledgeGraph, KnowledgeGraphElement, KnowledgeGraphRelation } from '../types';
import { ICONS } from '../constants';

interface KnowledgeGraphVisualizerProps {
  graph?: KnowledgeGraph;
}

const KnowledgeGraphVisualizer: React.FC<KnowledgeGraphVisualizerProps> = ({ graph }) => {
  if (!graph || (!graph.elements?.length && !graph.summary)) {
    return <p className="text-neutral/60 text-sm">Knowledge graph data is not yet available for this project.</p>;
  }

  return (
    <div className="space-y-4">
      {graph.summary && (
        <p className="text-sm text-neutral/80 italic p-3 bg-primary/5 rounded-md border border-primary/10">
          <strong>AI Summary of Graph:</strong> {graph.summary}
        </p>
      )}
      
      {graph.elements && graph.elements.length > 0 && (
        <div>
          <h4 className="text-md font-semibold text-neutral mb-2">Key Entities & Concepts ({graph.elements.length})</h4>
          <div className="flex flex-wrap gap-2">
            {graph.elements.map((el: KnowledgeGraphElement) => (
              <span
                key={el.id}
                className={`px-3 py-1 text-xs font-medium rounded-full shadow-sm
                  ${el.type === 'entity' ? 'bg-info/20 text-info' : 'bg-accent/20 text-accent-dark'}`}
                title={`Type: ${el.type}`}
              >
                {el.label}
              </span>
            ))}
          </div>
        </div>
      )}

      {graph.relations && graph.relations.length > 0 && (
         <div>
          <h4 className="text-md font-semibold text-neutral mb-2">Identified Relationships ({graph.relations.length})</h4>
          <ul className="space-y-1 text-sm">
            {graph.relations.map((rel: KnowledgeGraphRelation, index: number) => {
              const sourceNode = graph.elements?.find(e => e.id === rel.source);
              const targetNode = graph.elements?.find(e => e.id === rel.target);
              return (
                <li key={index} className="p-2 bg-secondary/50 rounded-md flex items-center space-x-2">
                  <span className={`font-medium ${sourceNode?.type === 'entity' ? 'text-info' : 'text-accent-dark'}`}>
                    {sourceNode?.label || rel.source}
                  </span>
                  <span className="text-neutral/60">&rarr;</span> 
                  <span className="italic text-primary">{rel.label}</span>
                  <span className="text-neutral/60">&rarr;</span> 
                  <span className={`font-medium ${targetNode?.type === 'entity' ? 'text-info' : 'text-accent-dark'}`}>
                    {targetNode?.label || rel.target}
                  </span>
                </li>
              );
            })}
          </ul>
        </div>
      )}
      <p className="text-xs text-neutral/50 mt-4">
        Note: This is a textual representation of the conceptual knowledge graph. Advanced visualization would typically involve interactive diagrams.
      </p>
    </div>
  );
};

export default KnowledgeGraphVisualizer;
    