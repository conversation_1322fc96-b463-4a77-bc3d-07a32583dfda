
import React from 'react';
import { AISuggestion } from '../types';
import { ICONS } from '../constants';

interface AISuggestionCardProps {
  suggestion: AISuggestion;
}

const AISuggestionCard: React.FC<AISuggestionCardProps> = ({ suggestion }) => {
  const getIconAndColor = () => {
    switch (suggestion.type) {
      case 'opportunity':
        return { icon: ICONS.LIGHTBULB, color: 'text-success', bg: 'bg-success/10 border-success/30' };
      case 'risk':
        return { icon: ICONS.INFO, color: 'text-error', bg: 'bg-error/10 border-error/30' };
      case 'next_step':
        return { icon: ICONS.PROJECT /* Placeholder, could be a checkmark or arrow */, color: 'text-info', bg: 'bg-info/10 border-info/30' };
      case 'synergy':
        return { icon: ICONS.GRAPH, color: 'text-accent', bg: 'bg-accent/10 border-accent/30' };
      default:
        return { icon: ICONS.AI_SPARKLE, color: 'text-primary', bg: 'bg-primary/10 border-primary/30' };
    }
  };

  const { icon, color, bg } = getIconAndColor();

  return (
    <div className={`p-4 rounded-lg border shadow-sm ${bg}`}>
      <div className={`flex items-start space-x-3`}>
        <span className={`flex-shrink-0 w-6 h-6 ${color}`}>{icon}</span>
        <div>
          <h4 className={`text-md font-semibold ${color}`}>{suggestion.title}</h4>
          <p className="text-sm text-neutral/80 mt-1">{suggestion.description}</p>
          {suggestion.priority && (
            <span className={`mt-2 inline-block px-2 py-0.5 text-xs font-medium rounded-full ${
              suggestion.priority === 'high' ? 'bg-error/80 text-base-100' :
              suggestion.priority === 'medium' ? 'bg-warning/80 text-base-100' :
              'bg-neutral/20 text-neutral/80'
            }`}>
              Priority: {suggestion.priority.charAt(0).toUpperCase() + suggestion.priority.slice(1)}
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default AISuggestionCard;
    