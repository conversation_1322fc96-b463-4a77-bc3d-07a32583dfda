
import React from 'react';
import { Link } from 'react-router-dom';
import { Project } from '../types';
import { ICONS } from '../constants';

interface ProjectCardProps {
  project: Project;
}

const ProjectCard: React.FC<ProjectCardProps> = ({ project }) => {
  const { initialAnalysis } = project;

  const getStatusColor = () => {
    switch (project.status) {
      case 'analyzing_initial':
      case 'analyzing_deep':
        return 'bg-yellow-400 animate-pulse-fast';
      case 'analyzed_initial':
      case 'analyzed_deep':
        return 'bg-success';
      case 'error':
        return 'bg-error';
      default:
        return 'bg-neutral/30';
    }
  };
  
  const getStatusText = () => {
     switch (project.status) {
      case 'analyzing_initial': return 'Initial AI Analysis...';
      case 'analyzed_initial': return 'Ready for Deep Dive';
      case 'analyzing_deep': return 'Deep AI Analysis...';
      case 'analyzed_deep': return 'Analysis Complete';
      case 'error': return 'Error in Analysis';
      default: return 'New';
    }
  }

  return (
    <Link to={`/project/${project.id}`} className="block hover:shadow-2xl transition-shadow duration-300">
      <div className="bg-base-100 rounded-lg shadow-lg overflow-hidden h-full flex flex-col">
        <div className={`p-1 text-center text-xs font-semibold text-base-100 ${getStatusColor()}`}>
          {getStatusText()}
        </div>
        <div className="p-5 flex-grow">
          <h3 className="text-xl font-semibold text-primary mb-2 flex items-center">
            {ICONS.PROJECT} <span className="ml-2">{project.name}</span>
          </h3>
          <p className="text-sm text-neutral/70 mb-3 h-16 overflow-hidden">{project.description}</p>
          
          {initialAnalysis && (
            <div className="mb-3 text-sm">
              <p className="text-neutral/90">
                <strong className="font-medium">AI Type Guess:</strong> {initialAnalysis.projectTypeGuess}
              </p>
              {initialAnalysis.summary && 
                <p className="text-neutral/80 mt-1 italic">
                  <strong className="font-medium not-italic">AI Summary:</strong> "{initialAnalysis.summary}"
                </p>
              }
            </div>
          )}
          
          {initialAnalysis && initialAnalysis.keywords.length > 0 && (
            <div>
              <h4 className="text-xs font-semibold text-neutral/60 uppercase mb-1">AI Keywords:</h4>
              <div className="flex flex-wrap gap-1">
                {initialAnalysis.keywords.slice(0, 5).map(keyword => (
                  <span key={keyword} className="px-2 py-0.5 bg-accent/20 text-accent-dark text-xs rounded-full">
                    {keyword}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
        <div className="p-4 bg-secondary/50 border-t border-neutral/10 text-right">
           <span className="text-sm text-primary hover:underline">View Details &rarr;</span>
        </div>
      </div>
    </Link>
  );
};

export default ProjectCard;
    