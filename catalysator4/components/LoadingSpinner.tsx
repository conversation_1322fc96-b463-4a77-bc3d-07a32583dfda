
import React from 'react';

interface LoadingSpinnerProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg';
  color?: string; // Tailwind text color class e.g. text-primary
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ message, size = 'md', color = 'text-primary' }) => {
  const sizeClasses = {
    sm: 'w-5 h-5 border-2',
    md: 'w-8 h-8 border-4',
    lg: 'w-12 h-12 border-4',
  };

  return (
    <div className="flex flex-col items-center justify-center my-4">
      <div
        className={`animate-spin rounded-full ${sizeClasses[size]} ${color} border-solid border-t-transparent`}
        role="status"
        aria-live="polite"
        aria-label={message || "Loading"}
      >
      </div>
      {message && <p className={`mt-3 text-sm ${color === 'text-base-100' ? 'text-base-100' : 'text-neutral/80'}`}>{message}</p>}
    </div>
  );
};

export default LoadingSpinner;
    