
import React, { createContext, useState, useContext, useCallback, useEffect, ReactNode } from 'react';
import { Project, ProjectFile, AIInitialAnalysis, AIStrategicOutput, ProjectTemplate, PortfolioAnalysis, ProjectsContextType } from '../types';
import { geminiService } from '../services/geminiService';
import { v4 as uuidv4 } from 'uuid';

// interface ProjectsContextType is now imported from types.ts

const ProjectsContext = createContext<ProjectsContextType | undefined>(undefined);

export const ProjectsProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [projects, setProjects] = useState<Project[]>(() => {
    const savedProjects = localStorage.getItem('aiProjects');
    return savedProjects ? JSON.parse(savedProjects) : [];
  });
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [portfolioAnalysis, setPortfolioAnalysis] = useState<PortfolioAnalysis | null>(null);
  const [isLoadingPortfolio, setIsLoadingPortfolio] = useState<boolean>(false);

  useEffect(() => {
    localStorage.setItem('aiProjects', JSON.stringify(projects));
  }, [projects]);

  const updateProjectState = useCallback((projectId: string, updates: Partial<Project>) => {
    setProjects(prev => prev.map(p => p.id === projectId ? { ...p, ...updates, updatedAt: new Date().toISOString() } : p));
  }, []);

  const addProject = useCallback(async (name: string, description: string, files: ProjectFile[]): Promise<string | undefined> => {
    setIsLoading(true);
    const newProjectId = uuidv4();
    
    let tempProject: Project = {
      id: newProjectId,
      name,
      description,
      files,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      dataCore: {},
      selectedTemplate: ProjectTemplate.STANDARD,
      status: 'analyzing_initial',
    };
    setProjects(prev => [...prev, tempProject]);

    try {
      const initialAnalysis: AIInitialAnalysis = await geminiService.getInitialAnalysis(name, description, files);
      tempProject = { ...tempProject, initialAnalysis, status: 'analyzed_initial', dataCore: {...tempProject.dataCore, projectSummary: initialAnalysis.summary} };
      updateProjectState(newProjectId, tempProject);
      setIsLoading(false);
      return newProjectId;
    } catch (error) {
      console.error("Error adding project:", error);
      const errorDetails = error instanceof Error ? error.message : String(error);
      updateProjectState(newProjectId, { status: 'error', errorDetails });
      setIsLoading(false);
      return undefined;
    }
  }, [updateProjectState]);

  const getProjectById = useCallback((id: string): Project | undefined => {
    return projects.find(p => p.id === id);
  }, [projects]);

  const updateProjectDataCore = useCallback((projectId: string, data: Record<string, any>) => {
    const project = getProjectById(projectId);
    if (project) {
      const updatedDataCore = { ...project.dataCore, ...data };
      updateProjectState(projectId, { dataCore: updatedDataCore });
    }
  }, [getProjectById, updateProjectState]);

  const runDeepAnalysis = useCallback(async (projectId: string) => {
    const project = getProjectById(projectId);
    if (!project) return;

    updateProjectState(projectId, { status: 'analyzing_deep' });
    setIsLoading(true); // Global loading for simplicity, or could be project-specific

    try {
      const strategicOutputCore: Omit<AIStrategicOutput, 'generatedImageUrl'> = await geminiService.performDeepAnalysis(project);
      let fullStrategicOutput: AIStrategicOutput = { ...strategicOutputCore, generatedImageUrl: project.strategicOutput?.generatedImageUrl };

      if (strategicOutputCore.projectPageNarrative && !strategicOutputCore.projectPageNarrative.startsWith("Deep analysis could not be performed")) {
         const imageUrl = await geminiService.generateProjectImage(project); // Pass project for context
         fullStrategicOutput = { ...strategicOutputCore, generatedImageUrl: imageUrl };
      }
      
      updateProjectState(projectId, {
        strategicOutput: fullStrategicOutput,
        selectedTemplate: strategicOutputCore.suggestedTemplate || project.selectedTemplate,
        dataCore: {
          ...project.dataCore,
          projectNarrative: fullStrategicOutput.projectPageNarrative,
          knowledgeGraphData: fullStrategicOutput.knowledgeGraph,
          strategicSuggestions: fullStrategicOutput.strategicSuggestions,
        },
        status: 'analyzed_deep'
      });
    } catch (error) {
      console.error("Error during deep analysis orchestration:", error);
      const errorDetails = error instanceof Error ? error.message : String(error);
      updateProjectState(projectId, { status: 'error', errorDetails });
    } finally {
      setIsLoading(false);
    }
  }, [getProjectById, updateProjectState]);

  const setSelectedTemplate = useCallback((projectId: string, template: ProjectTemplate) => {
    updateProjectState(projectId, { selectedTemplate: template });
  }, [updateProjectState]);

  const analyzePortfolio = useCallback(async () => {
    if (projects.length < 1) { 
        setPortfolioAnalysis(null);
        return;
    }
    setIsLoadingPortfolio(true);
    try {
        const analysisResult = await geminiService.analyzePortfolioSynergies(projects);
        setPortfolioAnalysis(analysisResult);
    } catch (error) {
        console.error("Error analyzing portfolio:", error);
        const errorDescription = error instanceof Error ? error.message : String(error);
        setPortfolioAnalysis({ 
            synergies: [], 
            conflicts: [{ projectIds: [], description: `Portfolio analysis failed: ${errorDescription}` }], 
            sharedResources: [] 
        });
    } finally {
        setIsLoadingPortfolio(false);
    }
  }, [projects]);

  const contextValue: ProjectsContextType = { 
    projects, 
    isLoading, 
    portfolioAnalysis, 
    isLoadingPortfolio, 
    addProject, 
    getProjectById, 
    updateProjectDataCore, 
    runDeepAnalysis, 
    setSelectedTemplate, 
    analyzePortfolio 
  };

  return React.createElement(
    ProjectsContext.Provider,
    { value: contextValue },
    children
  );
};

export const useProjects = (): ProjectsContextType => {
  const context = useContext(ProjectsContext);
  if (context === undefined) {
    throw new Error('useProjects must be used within a ProjectsProvider');
  }
  return context;
};
