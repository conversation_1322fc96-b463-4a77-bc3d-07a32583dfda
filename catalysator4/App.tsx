
import React from 'react';
import { Routes, Route, Link } from 'react-router-dom';
import Dashboard from './components/Dashboard';
import ProjectPage from './components/ProjectPage';
import Header from './components/Header';
import HowAIWorksPage from './components/HowAIWorksPage';

const App: React.FC = () => {
  return (
    <div className="min-h-screen flex flex-col bg-secondary">
      <Header />
      <main className="flex-grow container mx-auto p-4 sm:p-6 lg:p-8">
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/project/:projectId" element={<ProjectPage />} />
          <Route path="/how-ai-works" element={<HowAIWorksPage />} />
        </Routes>
      </main>
      <footer className="py-6 text-center text-neutral/70 text-sm">
        <p>&copy; {new Date().getFullYear()} AI Project Catalyst. Empowering Your Vision.</p>
        <p className="mt-1">
          <Link to="/how-ai-works" className="hover:text-primary underline">How our AI Learns & Enhances Projects</Link>
        </p>
      </footer>
    </div>
  );
};

export default App;
    