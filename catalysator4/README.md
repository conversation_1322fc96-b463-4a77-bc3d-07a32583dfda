# AI Project Catalyst

An intelligent project management and AI-powered analysis tool built with React, TypeScript, and Vite.

## Features

- 🤖 AI-powered project analysis using Google Gemini
- 📊 Interactive dashboard with project insights
- 🎯 Smart suggestions and recommendations
- 🌐 Multi-language support (i18n)
- 📱 Responsive design with Tailwind CSS
- 🔄 Real-time project status tracking

## Run Locally

**Prerequisites:** Node.js 18+ and npm

1. **Clone the repository:**
   ```bash
   git clone <your-repo-url>
   cd catalysator4
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Set up environment variables:**
   - Copy `.env.example` to `.env.local`
   - Get your Gemini API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Update `.env.local` with your API key:
   ```
   GEMINI_API_KEY=your_actual_api_key_here
   ```

4. **Run the development server:**
   ```bash
   npm run dev
   ```

5. **Open your browser:**
   Navigate to `http://localhost:5173`

## Build for Production

```bash
npm run build
```

The built files will be in the `dist` directory.

## Deploy to Vercel

### Option 1: Deploy via Vercel CLI

1. **Install Vercel CLI:**
   ```bash
   npm i -g vercel
   ```

2. **Deploy:**
   ```bash
   vercel
   ```

3. **Set environment variables in Vercel dashboard:**
   - Go to your project settings in Vercel
   - Add `GEMINI_API_KEY` environment variable

### Option 2: Deploy via GitHub Integration

1. **Push to GitHub:**
   ```bash
   git add .
   git commit -m "Initial commit"
   git push origin main
   ```

2. **Connect to Vercel:**
   - Go to [Vercel Dashboard](https://vercel.com/dashboard)
   - Click "New Project"
   - Import your GitHub repository
   - Set `GEMINI_API_KEY` environment variable
   - Deploy!

## Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `GEMINI_API_KEY` | Google Gemini AI API key | Yes |

## Tech Stack

- **Frontend:** React 19, TypeScript, Vite
- **Styling:** Tailwind CSS
- **AI:** Google Gemini API
- **Routing:** React Router DOM
- **Internationalization:** i18next
- **Build Tool:** Vite
- **Deployment:** Vercel

## Project Structure

```
catalysator4/
├── src/
│   ├── components/     # React components
│   ├── hooks/         # Custom React hooks
│   ├── services/      # API services
│   ├── locales/       # i18n translations
│   └── types.ts       # TypeScript type definitions
├── api/               # Backend API routes
├── public/            # Static assets
├── vercel.json        # Vercel deployment config
└── vite.config.ts     # Vite configuration
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.
