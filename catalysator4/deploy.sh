#!/bin/bash

# AI Project Catalyst Deployment Script
# This script helps automate the deployment process

set -e  # Exit on any error

echo "🚀 AI Project Catalyst Deployment Script"
echo "========================================"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: package.json not found. Please run this script from the catalysator4 directory."
    exit 1
fi

# Check if git is initialized
if [ ! -d ".git" ]; then
    echo "❌ Error: Git repository not found. Please run this script from a git repository."
    exit 1
fi

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo "🔍 Checking prerequisites..."

if ! command_exists git; then
    echo "❌ Git is not installed. Please install Git first."
    exit 1
fi

if ! command_exists node; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

if ! command_exists npm; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ Prerequisites check passed!"

# Check if .env.local exists and has GEMINI_API_KEY
if [ ! -f ".env.local" ]; then
    echo "⚠️  Warning: .env.local file not found."
    echo "Please create .env.local with your GEMINI_API_KEY before deploying."
    echo "Example: GEMINI_API_KEY=your_api_key_here"
    read -p "Do you want to continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
else
    if ! grep -q "GEMINI_API_KEY" .env.local; then
        echo "⚠️  Warning: GEMINI_API_KEY not found in .env.local"
        read -p "Do you want to continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Build the project to test
echo "🔨 Building project..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
else
    echo "❌ Build failed. Please fix the errors before deploying."
    exit 1
fi

# Check git status
echo "📋 Checking git status..."
if [ -n "$(git status --porcelain)" ]; then
    echo "📝 You have uncommitted changes. Let's commit them first."
    
    echo "Current changes:"
    git status --short
    
    read -p "Do you want to commit these changes? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        read -p "Enter commit message: " commit_message
        if [ -z "$commit_message" ]; then
            commit_message="Update application for deployment"
        fi
        
        git add .
        git commit -m "$commit_message"
        echo "✅ Changes committed!"
    else
        echo "⚠️  Proceeding with uncommitted changes..."
    fi
fi

# Check if remote origin exists
if ! git remote get-url origin >/dev/null 2>&1; then
    echo "🔗 No remote origin found. You need to set up a GitHub repository."
    echo ""
    echo "Steps to set up GitHub repository:"
    echo "1. Go to https://github.com/new"
    echo "2. Create a new repository (e.g., 'katalyzator4')"
    echo "3. Copy the repository URL"
    echo ""
    read -p "Enter your GitHub repository URL: " repo_url
    
    if [ -n "$repo_url" ]; then
        git remote add origin "$repo_url"
        echo "✅ Remote origin added!"
    else
        echo "❌ No repository URL provided. Skipping GitHub push."
        exit 1
    fi
fi

# Push to GitHub
echo "📤 Pushing to GitHub..."
git branch -M main
git push -u origin main

if [ $? -eq 0 ]; then
    echo "✅ Successfully pushed to GitHub!"
else
    echo "❌ Failed to push to GitHub. Please check your repository URL and permissions."
    exit 1
fi

# Vercel deployment
echo ""
echo "🌐 Vercel Deployment Options:"
echo "1. Deploy via Vercel CLI (requires vercel CLI installed)"
echo "2. Manual deployment via Vercel Dashboard"
echo "3. Skip Vercel deployment"

read -p "Choose an option (1-3): " -n 1 -r
echo

case $REPLY in
    1)
        if command_exists vercel; then
            echo "🚀 Deploying with Vercel CLI..."
            vercel --prod
        else
            echo "❌ Vercel CLI not found. Installing..."
            npm install -g vercel
            echo "🚀 Deploying with Vercel CLI..."
            vercel --prod
        fi
        ;;
    2)
        echo "📖 Manual Deployment Instructions:"
        echo "1. Go to https://vercel.com/dashboard"
        echo "2. Click 'New Project'"
        echo "3. Import your GitHub repository"
        echo "4. Set Root Directory to 'catalysator4'"
        echo "5. Add environment variable: GEMINI_API_KEY"
        echo "6. Deploy!"
        ;;
    3)
        echo "⏭️  Skipping Vercel deployment."
        ;;
    *)
        echo "❌ Invalid option. Skipping Vercel deployment."
        ;;
esac

echo ""
echo "🎉 Deployment script completed!"
echo ""
echo "📋 Summary:"
echo "✅ Dependencies installed"
echo "✅ Project built successfully"
echo "✅ Code committed to git"
echo "✅ Code pushed to GitHub"
echo ""
echo "🔗 Your GitHub repository: $(git remote get-url origin)"
echo ""
echo "📚 For detailed deployment instructions, see DEPLOYMENT.md"
echo ""
echo "🚀 Your application is ready for deployment!"
