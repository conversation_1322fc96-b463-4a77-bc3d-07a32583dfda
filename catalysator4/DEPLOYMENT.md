# Deployment Guide for AI Project Catalyst

This guide will help you deploy your AI Project Catalyst application to GitHub and Vercel.

## Prerequisites

- Git installed on your system
- GitHub account
- Vercel account (free tier available)
- Node.js 18+ installed
- Your Gemini API key

## Step 1: Create GitHub Repository

1. **Go to GitHub** and create a new repository:
   - Repository name: `katalyzator4` (or your preferred name)
   - Description: "AI-powered project management and analysis tool"
   - Set to Public or Private (your choice)
   - Don't initialize with README (we already have one)

2. **Copy the repository URL** (it will look like: `https://github.com/yourusername/katalyzator4.git`)

## Step 2: Connect Local Repository to GitHub

Run these commands in your terminal from the catalysator4 directory:

```bash
# Add the remote repository
git remote add origin https://github.com/yourusername/katalyzator4.git

# Push to GitHub
git branch -M main
git push -u origin main
```

Replace `yourusername` with your actual GitHub username and `katalyzator4` with your repository name with your repository name.

## Step 3: Deploy to Vercel

### Option A: Deploy via Vercel Dashboard (Recommended)

1. **Go to [Vercel Dashboard](https://vercel.com/dashboard)**

2. **Click "New Project"**

3. **Import your GitHub repository:**
   - Select "Import Git Repository"
   - Choose your `katalyzator4` repository
   - Click "Import"

4. **Configure the project:**
   - Project Name: `katalyzator4` (or your preferred name)
   - Framework Preset: Vite (should be auto-detected)
   - Root Directory: `catalysator4` (IMPORTANT!)
   - Build Command: `npm run build`
   - Output Directory: `dist`

5. **Add Environment Variables:**
   - Click "Environment Variables"
   - Add: `GEMINI_API_KEY` = `your_actual_gemini_api_key`
   - Make sure to add it for Production, Preview, and Development

6. **Click "Deploy"**

### Option B: Deploy via Vercel CLI

1. **Install Vercel CLI:**
   ```bash
   npm i -g vercel
   ```

2. **Login to Vercel:**
   ```bash
   vercel login
   ```

3. **Deploy from the catalysator4 directory:**
   ```bash
   vercel
   ```

4. **Follow the prompts:**
   - Link to existing project? No
   - Project name: `ai-project-catalyst`
   - Directory: `./` (current directory)

5. **Set environment variables:**
   ```bash
   vercel env add GEMINI_API_KEY
   ```
   Enter your Gemini API key when prompted.

6. **Deploy to production:**
   ```bash
   vercel --prod
   ```

## Step 4: Verify Deployment

1. **Check your Vercel dashboard** for the deployment URL
2. **Visit your live application**
3. **Test the AI features** to ensure the API key is working
4. **Check the console** for any errors

## Environment Variables Setup

Your application requires these environment variables:

| Variable | Description | Required |
|----------|-------------|----------|
| `GEMINI_API_KEY` | Google Gemini AI API key | Yes |

### Getting a Gemini API Key

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy the generated key
5. Add it to your Vercel environment variables

## Troubleshooting

### Common Issues

1. **Build fails with "Module not found":**
   - Ensure all dependencies are in package.json
   - Run `npm install` locally to verify

2. **API key not working:**
   - Verify the key is correctly set in Vercel environment variables
   - Check that the key has proper permissions in Google AI Studio

3. **404 errors on page refresh:**
   - The vercel.json file should handle this with SPA routing
   - Verify the vercel.json file is in the root directory

4. **Build directory issues:**
   - Make sure Root Directory is set to `catalysator4` in Vercel
   - Verify Build Command is `npm run build`
   - Verify Output Directory is `dist`

### Support

If you encounter issues:
1. Check the Vercel deployment logs
2. Verify all environment variables are set
3. Test the build locally with `npm run build`
4. Check the browser console for JavaScript errors

## Next Steps

After successful deployment:
1. Set up custom domain (optional)
2. Configure analytics
3. Set up monitoring
4. Add more environment-specific configurations

## Updating Your Deployment

To update your deployed application:

1. **Make changes to your code**
2. **Commit and push to GitHub:**
   ```bash
   git add .
   git commit -m "Your update message"
   git push origin main
   ```
3. **Vercel will automatically redeploy** (if connected to GitHub)

Your application is now live and ready to use! 🚀
