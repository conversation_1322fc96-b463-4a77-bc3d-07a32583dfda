
export interface ProjectFile {
  id: string;
  name: string;
  type: string; // e.g., 'application/pdf', 'text/plain'
  size: number; // in bytes
}

export interface AIInitialAnalysis {
  projectTypeGuess: string;
  keywords: string[];
  summary?: string;
}

export interface KnowledgeGraphElement {
  id: string;
  label: string;
  type: 'entity' | 'concept';
}

export interface KnowledgeGraphRelation {
  source: string; // id of source element
  target: string; // id of target element
  label: string;
}

export interface KnowledgeGraph {
  elements: KnowledgeGraphElement[];
  relations: KnowledgeGraphRelation[];
  summary?: string;
}

export interface AIStrategicOutput {
  projectPageNarrative: string;
  suggestedTemplate: ProjectTemplate;
  generatedImageUrl?: string;
  knowledgeGraph: KnowledgeGraph;
  strategicSuggestions: AISuggestion[];
}

export interface AISuggestion {
  id: string;
  title: string;
  description: string;
  type: 'opportunity' | 'risk' | 'next_step' | 'synergy';
  priority?: 'high' | 'medium' | 'low';
}

export enum ProjectTemplate {
  STANDARD = 'Standard Overview',
  VISUAL_HEAVY = 'Visual-Heavy Showcase',
  DATA_FOCUSED = 'Data-Focused Report',
  NARRATIVE_RICH = 'Narrative-Rich Story',
}

export interface Project {
  id: string;
  name: string;
  description: string;
  files: ProjectFile[];
  createdAt: string;
  updatedAt: string;
  initialAnalysis?: AIInitialAnalysis;
  strategicOutput?: AIStrategicOutput;
  dataCore: { // Represents the dynamic .json, updated by AI
    projectSummary?: string;
    keyInsights?: string[];
    knowledgeGraphData?: KnowledgeGraph;
    [key: string]: any;
  };
  selectedTemplate: ProjectTemplate;
  status: 'new' | 'analyzing_initial' | 'analyzed_initial' | 'analyzing_deep' | 'analyzed_deep' | 'error';
  errorDetails?: string;
}

export interface PortfolioAnalysis {
  synergies: { projectIds: string[]; description: string }[];
  conflicts: { projectIds: string[]; description: string }[];
  sharedResources: { resource: string; projectIds: string[]; description: string }[];
}

// Moved from hooks/useProjects.ts to be centrally defined and exportable
export interface ProjectsContextType {
  projects: Project[];
  isLoading: boolean;
  portfolioAnalysis: PortfolioAnalysis | null;
  isLoadingPortfolio: boolean;
  addProject: (name: string, description: string, files: ProjectFile[]) => Promise<string | undefined>;
  getProjectById: (id: string) => Project | undefined;
  updateProjectDataCore: (projectId: string, data: Record<string, any>) => void;
  runDeepAnalysis: (projectId: string) => Promise<void>;
  setSelectedTemplate: (projectId: string, template: ProjectTemplate) => void;
  analyzePortfolio: () => Promise<void>;
}
