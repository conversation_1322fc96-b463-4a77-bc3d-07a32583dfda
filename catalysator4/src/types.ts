export interface ProjectFile {
  id: string;
  name: string;
  type: string; // e.g., 'application/pdf', 'text/plain', 'text/markdown', 'text/html', 'image/jpeg', 'image/png'
  size: number; // in bytes
  content?: string; // Optional: For storing text content directly (e.g., markdown, HTML) or base64 image data (data:image/...)
}

export interface AIInitialAnalysis {
  projectTypeGuess: string;
  keywords: string[];
  summary?: string;
}

export interface KnowledgeGraphElement {
  id: string;
  label: string;
  type: 'entity' | 'concept';
}

export interface KnowledgeGraphRelation {
  source: string; // id of source element
  target: string; // id of target element
  label: string;
}

export interface KnowledgeGraph {
  elements: KnowledgeGraphElement[];
  relations: KnowledgeGraphRelation[];
  summary?: string;
}

export interface AIAssistantPrompt {
  id: string;
  title: string; 
  description: string; 
  promptTemplate: string; 
  category: string; 
  requiresUserInput: boolean;
  requiresContextInput: boolean;
}

export interface AIAssistantPromptCategory {
  categoryTitle: string; 
  prompts: AIAssistantPrompt[];
}

export interface GeneratedImageDetails {
  url: string; // data:image/jpeg;base64,...
  prompt: string; // The prompt used to generate this specific image
  alternatives?: string[]; // Array of base64 image data URLs for alternatives
}

export interface AIStrategicOutput {
  projectPageNarrative: string;
  suggestedTemplate: ProjectTemplate;
  generatedImage?: GeneratedImageDetails;    // Main project image
  generatedLogo?: GeneratedImageDetails;     // Project logo
  generatedIcon?: GeneratedImageDetails;     // Project icon (e.g., favicon)
  generatedBanner?: GeneratedImageDetails;   // Project banner
  knowledgeGraph: KnowledgeGraph;
  strategicSuggestions: AISuggestion[];
  suggestedAssistantPrompts?: AIAssistantPromptCategory[];
}

export interface AISuggestion {
  id: string;
  title: string;
  description: string;
  type: 'opportunity' | 'risk' | 'next_step' | 'synergy';
  priority?: 'high' | 'medium' | 'low';
}

export enum ProjectTemplate {
  STANDARD = 'STANDARD',
  VISUAL_HEAVY = 'VISUAL_HEAVY',
  DATA_FOCUSED = 'DATA_FOCUSED',
  NARRATIVE_RICH = 'NARRATIVE_RICH',
}

export interface AIAssistantNote {
  id: string;
  title: string; 
  content: string; 
  promptUsed: string; 
  contextProvided?: string; 
  createdAt: string;
}

// For AIAssistantPanel - Deprecated if AIAssistantNote is used directly in dataCore
export interface AssistantMessage {
  id: string;
  type: 'user' | 'ai' | 'system';
  text: string;
  timestamp: string;
  promptUsed?: string;
  contextProvided?: string;
}

export type DeepAnalysisSubStage = 
  | 'narrative' 
  | 'knowledgeGraph' 
  | 'suggestions' 
  | 'assistantPrompts' 
  | 'mainImage' 
  | 'logo'
  | 'aiSpec' // New for AI Developer Spec
  | 'blogPost' // New for Blog Post
  | 'landingPageContent' // New for Landing Page (content part)
  | 'landingPageVisualSpec' // New for Landing Page (visual spec part)
  | null;

export type ProjectStatus =
  | 'new'
  | 'analyzing_initial'
  | 'analyzed_initial'
  | 'analyzing_deep' // Generic deep analysis state
  | 'analyzing_deep_narrative'
  | 'analyzing_deep_knowledge_graph'
  | 'analyzing_deep_suggestions'
  | 'analyzing_deep_assistant_prompts'
  | 'analyzing_deep_main_image'
  | 'analyzing_deep_logo'
  | 'analyzing_deep_ai_spec' // New status for AI Developer Spec generation
  | 'generating_ai_dev_spec' // Explicit status while modal is open and actively generating
  | 'analyzing_deep_blog_post' // New status for Blog Post generation
  | 'generating_blog_post' // Explicit status for blog post
  | 'analyzing_deep_landing_page_content' // New status for Landing Page content generation
  | 'generating_landing_page_content' // Explicit status for landing page content
  | 'analyzing_deep_landing_page_visual_spec' // New for Landing Page visual spec generation
  | 'generating_landing_page_visual_spec' // Explicit for landing page visual spec
  | 'analyzed_deep'
  | 'error';

export type LogoStyleOptionValue = 'modern' | 'vintage' | 'minimalist' | 'playful' | 'luxury' | 'traditional' | 'futuristic' | 'organic' | 'other';
export type LogoMoodOptionValue = 'trustworthy' | 'energetic' | 'calm' | 'innovative' | 'professional' | 'playful_mood' | 'luxury_mood' | 'natural' | 'other_mood';


export interface LogoBriefData {
  brandName: string;
  industry: string;
  targetAudience: string;
  style: LogoStyleOptionValue;
  styleOther?: string; // Used if style is 'other'
  preferredColors: string;
  avoidColors?: boolean; // Corresponds to "neviem" checkbox
  mood: LogoMoodOptionValue;
  moodOther?: string; // Used if mood is 'other_mood'
  specificElements: string;
  avoidElements: string;
}

// --- AI Asset Generator Form Data Types ---
export interface AIDevSpecFormData {
  appName: string;
  mainPurpose: string;
  industry: string;
  targetAudience: string;
  usp: string; // Unique Selling Proposition
  coreFeatures: string; // Could be a list or detailed text
  criticalRequirements: string; // Security, scalability, performance
  techStackHighLevel: string; // Preferred lang, frameworks, DB, cloud
  architectureType: 'monolith' | 'microservices' | 'serverless' | 'other_arch' | '';
  userFlows: string;
  uiUxHighLevel: string;
  implementationDeployment: string; // CI/CD, hosting, monitoring
  otherRequirements: string; // Testing, docs, maintenance
}

export interface BlogPostFormData {
  topic: string;
  postGoal: 'educate' | 'promote' | 'inform' | 'other_goal' | '';
  postLength: 'short' | 'medium' | 'long' | ''; // ~300, ~700, ~1200 words
  targetAudienceBlog: string;
  tone: 'formal' | 'friendly' | 'technical' | 'motivational' | 'other_tone' | '';
  keyMessages: string;
  preferredKeywords: string; // Comma-separated
  postStructure: string; // User can suggest sections
  ctaText: string;
  ctaLink: string;
}

export interface LandingPageFormData {
  pageName: string;
  mainPurposePage: string;
  targetAudiencePage: string;
  keyMessagesUSP: string;
  designStyle: 'modern' | 'minimalist' | 'luxury' | 'playful_lp' | 'other_style_lp' | '';
  primaryColor: string; // HEX
  secondaryColor: string; // HEX
  typographyPreference: 'serif' | 'sans-serif' | 'mixed' | 'other_typo' | '';
  requiredSections: string[]; // e.g., ['Hero', 'Benefits', 'HowItWorks', 'Reviews', 'FAQ', 'Pricing', 'Contact']
  sectionContentHints: Record<string, string>; // e.g., { "Hero": "Focus on innovation and ease of use." }
  ctaTextPage: string;
  ctaLinkPage: string;
  animationPreference: 'none_anim' | 'subtle' | 'dynamic' | '';
  navMenuContent: string; // Items for navigation
  footerContent: string; // Content for footer
  seoKeywords: string;
  metaTitle: string;
  metaDescription: string;
}
// --- End AI Asset Generator Form Data Types ---


export interface Project {
  id: string;
  name: string;
  description: string; 
  files: ProjectFile[];
  createdAt: string;
  updatedAt: string;
  initialAnalysis?: AIInitialAnalysis;
  strategicOutput?: AIStrategicOutput;
  dataCore: { 
    projectSummary?: string;
    keyInsights?: string[];
    knowledgeGraphData?: KnowledgeGraph;
    assistantNotes: AIAssistantNote[]; 
    logoBrief?: LogoBriefData; // To store user's saved logo brief
    // Form data for asset generators could be stored here if partially filled
    aiDevSpecFormData?: Partial<AIDevSpecFormData>;
    blogPostFormData?: Partial<BlogPostFormData>;
    landingPageFormData?: Partial<LandingPageFormData>;
    [key: string]: any;
  };
  selectedTemplate: ProjectTemplate;
  status: ProjectStatus;
  deepAnalysisSubStage?: DeepAnalysisSubStage; 
  detailedStatusMessageKey?: string; 
  errorDetails?: string;
  isFavorite: boolean;
  lastInteractionTimestamp: string; 
}

export interface PortfolioAnalysis {
  synergies: { projectIds: string[]; description: string }[];
  conflicts: { projectIds: string[]; description: string }[];
  sharedResources: { resource: string; projectIds: string[]; description: string }[];
}

export interface ProjectsContextType {
  projects: Project[];
  isLoading: boolean;
  portfolioAnalysis: PortfolioAnalysis | null;
  isLoadingPortfolio: boolean;
  addProject: (name: string, description: string, files: ProjectFile[], isFromPromptGenerator?: boolean) => Promise<string | undefined>;
  getProjectById: (id: string) => Project | undefined;
  updateProjectDataCore: (projectId: string, data: Record<string, any>) => void;
  runDeepAnalysis: (projectId: string) => Promise<void>;
  setSelectedTemplate: (projectId: string, template: ProjectTemplate) => void;
  analyzePortfolio: () => Promise<void>;
  executeProjectAssistantPrompt: (projectId: string, promptTemplate: string, userInput: string, contextInput?: string) => Promise<string>;
  addAssistantNoteToDataCore: (projectId: string, title: string, content: string, promptUsed: string, contextProvided?: string) => void;
  toggleFavoriteProject: (projectId: string) => void;
  addConceptualFileToProject: (projectId: string, fileName: string, fileType: string, fileSize: number, fileContent: string) => void;
  updateProjectGeneratedImage: (projectId: string, imageType: 'main' | 'logo' | 'icon' | 'banner', details: GeneratedImageDetails) => void;
  generateAndSetProjectLogo: (projectId: string, brief: LogoBriefData) => Promise<void>;
  fetchAIAssistedLogoBrief: (projectId: string) => Promise<Partial<LogoBriefData> | null>; 
  saveLogoBriefToDataCore: (projectId: string, brief: LogoBriefData) => void; 
  // Functions for new Asset Generator
  generateAndSaveAIDevSpec: (projectId: string, formData: AIDevSpecFormData) => Promise<void>;
  generateAndSaveBlogPost: (projectId: string, formData: BlogPostFormData) => Promise<void>;
  generateAndSaveLandingPage: (projectId: string, formData: LandingPageFormData) => Promise<void>;
  // AI assistance for form filling
  getAIAssistanceForAIDevSpecForm: (projectContext: Project) => Promise<Partial<AIDevSpecFormData>>;
  getAIAssistanceForBlogPostForm: (projectContext: Project) => Promise<Partial<BlogPostFormData>>;
  getAIAssistanceForLandingPageForm: (projectContext: Project) => Promise<Partial<LandingPageFormData>>;
  // File management
  deleteProjectFile: (projectId: string, fileId: string) => void;
  duplicateProjectFile: (projectId: string, fileId: string) => void;
  updateProjectFileContent: (projectId: string, fileId: string, newContent: string) => void;
}

// Notification System Types
export type NotificationType = 'success' | 'error' | 'info' | 'warning' | 'api_quota_exceeded'; // Added api_quota_exceeded

export interface NotificationMessage {
  id: string;
  type: NotificationType;
  message: string; // Main message content
  title?: string; // Optional title
  duration?: number; // Optional duration in ms, defaults to 5000
}

export interface NotificationContextType {
  notifications: NotificationMessage[];
  showNotification: (notification: Omit<NotificationMessage, 'id'>) => void;
  removeNotification: (id: string) => void;
}