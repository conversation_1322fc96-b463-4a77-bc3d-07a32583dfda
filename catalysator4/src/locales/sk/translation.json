{"appName": "AI Projekt Katalyzátor", "footerCopyright": "© {{year}} AI Projekt Katalyzátor. Posilňujeme Vašu Víziu.", "footerHowAiWorksLink": "Ako sa naša AI učí a vylepšuje projekty", "common": {"loading": "Načítava sa...", "error": "Chyba", "save": "Uložiť", "cancel": "Zrušiť", "delete": "Vymazať", "edit": "Upraviť", "create": "Vytvoriť", "close": "Zavrieť", "submit": "Odoslať", "confirm": "Potvrdiť", "warning": "Varovanie", "success": "Úspech", "info": "Informácia", "optional": "Voliteľné", "requiredField": "Toto pole je povinné", "selectOption": "Vyberte m<PERSON>...", "other": "<PERSON><PERSON>", "copyFailed": "Kopírovanie do schránky zlyhalo!", "showHelpFor": "Zobraziť pomocníka pre {{fieldName}}", "openMenu": "Otvoriť menu"}, "header": {"dashboard": "Dashboard", "newAppIdea": "Nový Nápad", "proAppka": "Profi Appka", "dynamicApp": "Dynamická Appka", "aiCapabilities": "AI Schopnosti"}, "dashboard": {"title": "Preh<PERSON><PERSON>", "newProjectButton": "Nový Projekt", "aiPortfolioOverview": {"title": "AI Prehľad Portfólia", "noAnalysisYet": "Analýza portfólia zatiaľ nie je dostupná.", "noInteractions": "AI nezistila žiadne významné interakcie na úrovni portfólia.", "refreshAnalysis": "Obnoviť Analýzu", "projectsAffected": "Projekty: {{ids}}", "synergies": "Potenciálne Synergie", "conflicts": "Potenciálne Konflikty", "sharedResources": "Príležitosti pre Zdieľané Zdroje"}, "portfolioAnalysisFailed": "Analýza portfólia zlyhala: {{error}}", "analyzing": "Ana<PERSON><PERSON><PERSON><PERSON>...", "emptyState": {"title": "Zatiaľ žiadne projekty!", "description": "Kliknite na \"Nový Projekt\" a naštartujte svoju víziu s pomocou AI."}, "createProjectButton": "Vytvorte svoj prvý projekt"}, "createProjectModal": {"title": "Vytvoriť Nový Projekt", "projectNameLabel": "Názov Projektu", "projectNamePlaceholder": "<PERSON>r. <PERSON><PERSON> Apliká<PERSON>", "projectDescriptionLabel": "Popis Projektu", "projectDescriptionPlaceholder": "Stručne opíšte hlavný cieľ a účel vášho projektu.", "uploadFilesLabel": "Priložiť Súbory (Konceptuálne)", "cancelButton": "Zrušiť", "createAndAnalyzeButton": "Vytvoriť & Analyzovať", "processingNewProject": "Spracováva sa nový projekt...", "errorNameDescriptionRequired": "Názov a popis projektu sú povinné."}, "projectCard": {"statusNew": "Nový", "statusAnalyzing_initial": "Počiatočná AI Analýza...", "statusAnalyzed_initial": "Pripravený na Hĺbkovú Analýzu", "statusAnalyzing_deep": "Hĺbková AI Analýza...", "statusAnalyzing_deep_narrative": "Generujem Príbeh...", "statusAnalyzing_deep_knowledge_graph": "Tvorí<PERSON>ý <PERSON>...", "statusAnalyzing_deep_suggestions": "Formulujem Návrhy...", "statusAnalyzing_deep_assistant_prompts": "Vytváram AI Prompty...", "statusAnalyzing_deep_main_image": "Generujem Hlavný Obrázok...", "statusAnalyzing_deep_logo": "Navrhujem Lo<PERSON>...", "statusAnalyzing_deep_ai_spec": "Tvorím AI Dev Špec<PERSON>u...", "statusGenerating_ai_dev_spec": "Tvorím AI Dev Špec<PERSON>u...", "statusAnalyzing_deep_blog_post": "Píšem Blogový Príspevok...", "statusGenerating_blog_post": "Píšem Blogový Príspevok...", "statusAnalyzing_deep_landing_page_content": "Navrhujem Landing Page...", "statusGenerating_landing_page_content": "Navrhujem Landing Page...", "statusAnalyzing_deep_landing_page_visual_spec": "Vizualizujem Landing Page...", "statusGenerating_landing_page_visual_spec": "Vizualizujem Landing Page...", "statusAnalyzed_deep": "<PERSON><PERSON><PERSON><PERSON>", "statusError": "Chyba v Analýze", "aiTypeGuess": "AI Odhad <PERSON>:", "aiSummary": "AI Zhrnutie:", "aiKeywords": "AI Kľúčové Slová:", "viewDetails": "Zobraziť Detaily", "quickView": "<PERSON><PERSON><PERSON><PERSON>", "quickViewAriaLabel": "<PERSON><PERSON><PERSON><PERSON> pre {{projectName}}", "markAsFavorite": "Označiť ako obľúbené", "unmarkAsFavorite": "Odznačiť ako obľúbené"}, "projectPage": {"loadingProject": "Načítavajú sa detaily projektu...", "projectNotFound": "Projekt nebol nájdený alebo sa stále načítava.", "goToDashboard": "Prejsť na Dashboard", "created": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastUpdated": "Posledná aktualizácia", "saveChanges": "Uložiť Zmeny", "saveChangesAlert": "<PERSON><PERSON><PERSON> projektu (konceptuálne) uložené!", "aiStrategicCenter": "AI Strategické Centrum", "aiStrategicCenterDescription": "Odomknite hlbšie poznatky a strategické výhody. Naša AI vykoná komplexnú sémantickú analýzu vašich projektových dát na generovanie cenných výstupov.", "runDeepAnalysis": "Spustiť Hĺbkovú Sémantickú Analýzu & Generovať Prehľady", "aiAnalyzing": "AI Analyzuje...", "aiAnalyzingNarrative": "Generujem Príbeh...", "aiAnalyzingKnowledgeGraph": "Tvorí<PERSON>ý <PERSON>...", "aiAnalyzingSuggestions": "Formulujem Návrhy...", "aiAnalyzingAssistantPrompts": "Vytváram AI Prompty...", "aiAnalyzingMainImage": "Generujem Hlavný Obrázok...", "aiAnalyzingLogo": "Navrhujem Lo<PERSON>...", "errorDuringAnalysis": "Chyba počas analýzy: {{errorDetails}}", "projectNarrativeTitle": "AI-Generovaný Projektový Príbeh", "generatedImageTitle": "AI-Generovaný Obrázok Projektu", "knowledgeGraphTitle": "AI-<PERSON><PERSON><PERSON><PERSON>", "strategicConsultantTitle": "AI Strategický Konzultant", "noStrategicSuggestions": "AI momentálne negenerovala žiadne špecifické strategické návrhy.", "displayTemplateTitle": "Šablóna Zobrazenia", "aiSuggested": "AI Navrhla:", "templateSelectionNote": "Poznámka: <PERSON><PERSON><PERSON> šablóny je konceptuálny. Skutočné vykreslenie obsahu na základe šablón by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> komplexnejšiu logiku.", "deepAnalysisPrompt": "<PERSON><PERSON><PERSON> \"Hĺbkovú Sémantickú Analýzu\" na generovanie projektového prí<PERSON>hu, o<PERSON><PERSON><PERSON><PERSON><PERSON>, znalostného grafu a strategických návrhov.", "filesTitle": "Súbory Projektu", "noFiles": "K tomuto projektu nie sú priradené žiadne súbory.", "deepAnalysisRequiredForFeature": "Pre použitie tejto funkcie je potrebné najskôr vykonať hĺbkovú analýzu.", "generatingDocumentation": "Generuje sa osnova dokumentácie...", "documentationOutlineGenerated": "Osnova dokumentácie bola vygenerovaná a pridaná k súborom projektu.", "generateDocumentationError": "Nepodarilo sa vygenerovať osnovu dokumentácie.", "documentationOutlineFileName": "{{projectName}}_Osnova_Dokumentacie.md", "blogPostFileName": "{{projectName}}_Blogovy_Prispevok.md", "aiDeveloperSpecFileName": "{{projectName}}_AI_Dev_Specifikacia.md", "generatingAIDevSpec": "Generuje sa AI Špecifikácia pre Vývojárov...", "generateAIDevSpecError": "Nepodarilo sa vygenerovať AI špecifikáciu pre vývojárov.", "generatingBlogPost": "Generuje sa Blogový Príspevok...", "generateBlogPostError": "Nepodarilo sa vygenerovať blogový príspevok.", "newAiInsightIndicatorTitle": "Dostupný nový AI prehľad", "downloadAllAsZip": "Stiahnuť Všetko ako ZIP", "generatingZip": "Generuje sa ZIP súbor...", "generatingZipMessage": "Vaše súbory sa zipujú. Sťahovanie sa začne čoskoro.", "noFilesToZip": "Nie sú k dispozícii žiadne súbory na zipovanie.", "noContentToZip": "Nie sú k dispozícii žiadne súbory s obsahom na zahrnutie do ZIPu.", "zipError": "Vyskytla sa chyba pri vytváraní ZIP súboru.", "downloadFileTooltip": "Stiahnuť {{fileName}}", "downloadButton": "Stiahnuť", "filePreview": {"title": "Náhľad Súboru", "noContent": "Tento súbor nemá obsah na zobrazenie náhľadu.", "unsupportedType": "Náhľad pre typ súboru '{{fileType}}' nie je priamo pod<PERSON>."}, "fileEdit": {"title": "Upraviť Súbor", "notEditable": "<PERSON>ria<PERSON> úpra<PERSON> nie je podporovaná pre typ súboru '{{fileType}}'.", "contentLabel": "<PERSON><PERSON><PERSON> {{fileName}}"}, "fileActions": {"previewTooltip": "Náh<PERSON><PERSON> s<PERSON> {{fileName}}", "editTooltip": "Upraviť súbor {{fileName}}", "copyTooltip": "Skopírovať obsah s<PERSON>u {{fileName}}", "duplicateTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> súbor {{fileName}}", "deleteTooltip": "Vymazať súbor {{fileName}}"}, "nav": {"header": "Prehľad Projektu", "blueprint": "Blueprint Projektu", "aiAssetGenerator": "AI Generátor <PERSON>", "aiDeveloperSpecification": "AI Dev <PERSON>", "blogPost": "Blogový Príspevok", "landingPage": "<PERSON>", "strategicCenter": "AI Strategické Centrum", "assistantPanel": "AI Asistent", "narrative": "AI Príbeh", "imageSection": "AI Obrázky", "projectLogo": "Logo Projektu", "projectIcon": "Ikona Projektu", "projectBanner": "Banner Projektu", "knowledgeGraph": "Znalost<PERSON><PERSON>", "suggestions": "AI Návrhy", "template": "Šablóna Zobrazenia", "files": "Súbory Projektu"}, "sideNavLabel": "Navigácia Projektu", "imageGeneration": {"generating": "Generujem {{asset}}...", "altMainImage": "AI-generovaná reprezentácia projektu {{projectName}}", "altLogo": "AI-generované logo pre {{projectName}}", "altIcon": "AI-generov<PERSON><PERSON> i<PERSON> pre {{projectName}}", "altBanner": "AI-generov<PERSON><PERSON> banner pre {{projectName}}", "showPrompt": "Zobraziť Prompt", "hidePrompt": "Skryť Prompt", "regenerateButton": "Generovať Znovu", "alternativesButton": "Získať Alternatívy", "alternativesTitle": "Alternatívy", "alternativeText": "Alternatíva", "noImageYet": "{{asset}} e<PERSON><PERSON> ne<PERSON>.", "generateButton": "Generovať {{asset}}", "generationFailed": "Generovanie {{asset}} zlyhalo.", "generationFailedError": "Generovanie {{asset}} zlyhalo: {{error}}", "noBasePrompt": "<PERSON>e je k dispozícii základný prompt na generovanie alternatív pre {{asset}}.", "noAlternatives": "Momentálne sa nepodarilo vygenerovať žiadne alternatívy pre {{asset}}."}, "assetGenerator": {"title": "AI Generátor <PERSON> Aktív", "description": "Použite AI na generovanie rôznych projektových aktív na základe kontextu vášho projektu alebo špecifických vstupov.", "generateLogoButton": "Generovať Logo", "generateAIDevSpecButton": "Generovať AI Dev Špec.", "generateBlogPostButton": "Generovať Blog", "generateLandingPageButton": "Generovať Landing Page", "contentNotYetGenerated": "{{assetName}} ešte nebol vygenerovaný. Použite formuláre vyššie na jeho vytvorenie.", "modal": {"fillWithAIButton": "Vyplniť s AI", "generateButton": "Generovať", "cancelButton": "Zrušiť", "aiDevSpec": {"title": "Generátor AI Špecifikácie pre Vývojárov", "formDescription": "Posky<PERSON><PERSON><PERSON>, aby AI mohla vygenerovať komplexnú technickú špecifikáciu pre váš projekt.", "appNameLabel": "Názov Aplikácie", "appNamePlaceholder": "napr. MojaAppka X", "mainPurposeLabel": "Hlavný <PERSON> / Riešený Problém", "mainPurposePlaceholder": "Opíšte hlavný cieľ alebo prob<PERSON>m, k<PERSON><PERSON> tá<PERSON> a<PERSON> rieš<PERSON>.", "industryLabel": "Prie<PERSON>sel / Doména", "industryPlaceholder": "<PERSON>r. <PERSON><PERSON><PERSON>tníctvo, E-commerce, Vzdelávanie", "targetAudienceLabel": "Cieľové Publikum", "targetAudiencePlaceholder": "Opíšte primárnych používateľov tejto aplikácie.", "uspLabel": "Unikátna <PERSON> (USP)", "uspPlaceholder": "Čo robí túto aplikáciu jedinečnou alebo lepšou ako alternatívy?", "coreFeaturesLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (oddelené čiarkou alebo zoznam)", "coreFeaturesPlaceholder": "napr. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>-<PERSON> <PERSON><PERSON>", "criticalRequirementsLabel": "<PERSON><PERSON><PERSON><PERSON> (Bezpečnosť, Škálovateľnosť, atď.)", "criticalRequirementsPlaceholder": "<PERSON>r. HIPAA <PERSON>, Zvládnuť 10k súbežných používateľov, Odozva pod sekundu", "techStackHighLevelLabel": "Preferovaný Technologický Zásobník (Vysoká Úroveň)", "techStackHighLevelPlaceholder": "<PERSON><PERSON>. <PERSON>, Node.js, PostgreSQL, AWS; alebo 'AI navrhne'", "architectureTypeLabel": "Preferovaný Typ Architektúry", "architectureOptions": {"monolith": "Monolit", "microservices": "Mikroslužby", "serverless": "Serverless"}, "userFlowsLabel": "Kľúčové <PERSON>vateľské Toky / Scenáre", "userFlowsPlaceholder": "Opíšte 2-3 dôležité interakcie alebo cesty používateľa.", "uiUxHighLevelLabel": "Vysokoúrovňové UI/UX Preferencie", "uiUxHighLevelPlaceholder": "napr. <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "implementationDeploymentLabel": "Poznámky k Implementácii a Nasadeniu", "implementationDeploymentPlaceholder": "napr. CI/CD s GitHub Actions, Nasadenie na Vercel, Potreba staging prostredia", "otherRequirementsLabel": "<PERSON><PERSON>davky alebo Obmedzenia", "otherRequirementsPlaceholder": "Akékoľvek ďalšie dôležité detaily alebo obmedzenia."}, "blogPost": {"title": "<PERSON><PERSON><PERSON><PERSON> Príspev<PERSON>", "formDescription": "Poskytnite detaily pre AI na vytvorenie blogového príspevku súvisiaceho s vaším projektom.", "topicLabel": "Téma Blogového Príspevku", "topicPlaceholder": "napr. <PERSON> {{projectName}}", "postGoalLabel": "<PERSON><PERSON><PERSON>", "goalOptions": {"educate": "Vzdelávať", "promote": "Propagovať", "inform": "Informovať"}, "postLengthLabel": "Požadovaná Dĺžka", "lengthOptions": {"short": "<PERSON><PERSON><PERSON><PERSON><PERSON> (~300 slov)", "medium": "St<PERSON><PERSON>ý (~700 slov)", "long": "<PERSON><PERSON><PERSON>ý (~1200 slov)"}, "targetAudienceBlogLabel": "Cieľové Publikum Blogu", "targetAudienceBlogPlaceholder": "<PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Potenci<PERSON>lni Investori, <PERSON><PERSON>jnos<PERSON>", "toneLabel": "<PERSON><PERSON>", "toneOptions": {"formal": "Formálny", "friendly": "Priateľský", "technical": "<PERSON><PERSON><PERSON>", "motivational": "Motivačný"}, "keyMessagesLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (oddelené čiarkou alebo odr<PERSON>)", "keyMessagesPlaceholder": "<PERSON>r. <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> X problém, Inovatívna technológia", "preferredKeywordsLabel": "Preferovan<PERSON> (oddelené <PERSON>)", "preferredKeywordsPlaceholder": "napr. AI, projekt<PERSON><PERSON> man<PERSON>ž<PERSON>, inovácia", "postStructureLabel": "Navrhovaná <PERSON>uktú<PERSON> / Sek<PERSON> (voliteľné)", "postStructurePlaceholder": "<PERSON><PERSON>. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>", "ctaTextLabel": "Text Výzvy na Akciu (CTA)", "ctaTextPlaceholder": "<PERSON>r. <PERSON><PERSON><PERSON>, Zaregistrujte sa Teraz", "ctaLinkLabel": "Odkaz Výzvy na Akciu (URL)", "ctaLinkPlaceholder": "https://priklad.sk/viac-info"}, "landingPage": {"title": "<PERSON><PERSON><PERSON><PERSON>", "formDescription": "Definujte štruktúru a obsahové námety pre landing page vášho projektu.", "pageNameLabel": "Názov Stránky / Interný Titulok", "pageNamePlaceholder": "<PERSON>r. <PERSON><PERSON><PERSON> {{projectName}}", "mainPurposePageLabel": "<PERSON><PERSON><PERSON><PERSON>", "mainPurposePagePlaceholder": "<PERSON>r. <PERSON><PERSON><PERSON><PERSON> registr<PERSON>, <PERSON><PERSON><PERSON><PERSON> novú funkciu, <PERSON><PERSON><PERSON><PERSON>y", "targetAudiencePageLabel": "Cieľové Publikum Stránky", "targetAudiencePagePlaceholder": "napr. <PERSON><PERSON><PERSON><PERSON>, Firemní <PERSON>", "keyMessagesUSPLabel": "Kľúčové Odkazy / USP na Zdôraznenie", "keyMessagesUSPPlaceholder": "<PERSON><PERSON><PERSON> 1-3 najdôlež<PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mali vedieť?", "designStyleLabel": "Preferovaný Dizajnový Štýl", "styleOptionsLp": {"modern": "Moderný & Čistý", "minimalist": "Minimalistický", "luxury": "Luxusný & Elegantný", "playful_lp": "Hravý & Pútavý"}, "primaryColorLabel": "Primárna <PERSON> (HEX alebo názov)", "primaryColorPlaceholder": "napr. #4A90E2 alebo '<PERSON>as<PERSON> Modrá'", "secondaryColorLabel": "Sekundárna <PERSON>ky (HEX alebo názov)", "secondaryColorPlaceholder": "napr. #F5A623 alebo '<PERSON><PERSON><PERSON><PERSON>'", "typographyPreferenceLabel": "Preferencia Typografie", "typoOptions": {"serif": "<PERSON><PERSON><PERSON><PERSON> (Tradičné, Elegantné)", "sans-serif": "<PERSON><PERSON> (Moderné, Čisté)", "mixed": "Kombinované (Pätkov<PERSON> pre nadpisy, <PERSON><PERSON> pätkové pre text)"}, "requiredSectionsLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> (oddelené <PERSON>)", "requiredSectionsPlaceholder": "<PERSON><PERSON>. <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Cenník, FAQ, Kontakt", "sectionContentHintsLabel": "Obsahové Námety pre Sekcie (NázovSekcie: Námet)", "sectionContentHintsPlaceholder": "napr. Hero: Zdôrazniť jednoduchosť použitia\\nFunkcie: Detailne opísať X, Y, Z", "ctaTextPageLabel": "Hlavný Text Výzvy na Akciu (CTA)", "ctaTextPagePlaceholder": "napr. <PERSON><PERSON><PERSON><PERSON><PERSON>, Vyžiadať Demo", "ctaLinkPageLabel": "Hlavný Odkaz/Cieľ CTA", "ctaLinkPagePlaceholder": "napr. /registracia alebo mailto:<EMAIL>", "animationPreferenceLabel": "Preferencia Animácií", "animOptions": {"none_anim": "Žiadne", "subtle": "Jemné & Plynulé", "dynamic": "Dynamické & Pútavé"}, "navMenuContentLabel": "Položky Navigač<PERSON> (oddelené č<PERSON>u)", "navMenuContentPlaceholder": "<PERSON><PERSON>. <PERSON>, <PERSON>, Služby, Blog, Kontakt", "footerContentLabel": "<PERSON><PERSON><PERSON> (Copyright, odkazy, atď.)", "footerContentPlaceholder": "napr. © {{year}} {{projectName}}. Všetky práva vyhradené. Ochrana osobných údajov.", "seoKeywordsLabel": "SEO Kľúčové Slová (oddelené č<PERSON>u)", "seoKeywordsPlaceholder": "napr. <PERSON>, projekt<PERSON><PERSON>, produktivita", "metaTitleLabel": "SEO Meta Titulok", "metaTitlePlaceholder": "Navrhnite stručný a pútavý titulok pre vyhľadávače.", "metaDescriptionLabel": "SEO Meta <PERSON>", "metaDescriptionPlaceholder": "Navrhn<PERSON> stru<PERSON><PERSON><PERSON> (max 160 znakov) pre vyhľadávače."}}}, "landingPage": {"title": "<PERSON>", "generatingButton": "Generujem Landing Page...", "specTitle": "Špecifikácia Landing Page", "previewTitle": "Náh<PERSON>ad Landing Page pre {{projectName}}", "fileNameDefaultHTML": "{{projectName}}_Landing_Page.html", "specFileNameDefaultMD": "{{projectName}}_Landing_Page_Spec.md", "generationError": "Nepodarilo sa vygenerovať Landing Page.", "generationErrorInvalidHTML": "Odpoveď AI pre landing page nie je platný HTML kód.", "defaultTitle": "{{projectName}} - <PERSON> Page"}, "aiDeveloperSpecificationTitle": "AI Špecifikácia pre Vývojárov"}, "knowledgeGraph": {"aiSummary": "AI Zhrnutie Grafu:", "keyEntitiesAndConcepts": "Kľúčové Entity & Koncepty ({{count}})", "identifiedRelationships": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ({{count}})", "notAvailable": "Dáta znalostného grafu pre tento projekt zatiaľ nie sú dostupné.", "textualRepresentationNote": "Poznámka: <PERSON><PERSON> je textová reprezentácia konceptuálneho znalostného grafu.", "typeLabel": "<PERSON><PERSON>"}, "aiAssistant": {"title": "AI Projektový Asistent", "basicAssistantDescription": "Použite základné prompty alebo prepnite na Super Asistenta pre pomoc špecifickú pre projekt po hĺbkovej analýze.", "superAssistantDescription": "Váš Super Asistent je pripravený! Použite tieto prispôsobené prompty alebo si vytvorte vlastné.", "superAssistantNotReadyDetailed": "Super Asistent poskytuje prompty špecifické pre projekt po vykonaní 'Hĺbkovej Sémantickej Analýzy'. Spustite analýzu na odomknutie jeho plného potenciálu.", "tabs": {"basic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "super": "Super Asistent", "notes": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "selectPromptToStart": "<PERSON><PERSON><PERSON><PERSON> prompt zľava pre začiatok.", "userInputLabel": "<PERSON><PERSON><PERSON> / Otázka:", "userInputPlaceholder": "Napíšte vašu požiadavku alebo detaily sem...", "contextInputLabel": "Dodatoč<PERSON><PERSON> (Voliteľné):", "contextInputPlaceholder": "Vložte rozsiahlejší text, kód alebo časti dokumentov sem...", "executePrompt": "Spustiť Prompt", "processing": "AI Spracováva...", "aiResponseTitle": "Odpoveď AI:", "errorUserInputRequired": "Pre tento prompt je pot<PERSON><PERSON>ný vstup od používateľa.", "saveThisResponse": "Uložiť túto odpoveď ako poznámku", "saveNoteTitleLabel": "Názov Poznámky:", "saveNoteTitlePlaceholder": "Poznámka pre: {{promptTitle}}", "saveResponseAsNote": "Uložiť Poznámku", "noteSaved": "Odpoveď AI bola uložená ako poznámka.", "basicPrompts": {"titleSection": "Všeobecné <PERSON>", "summarize": {"title": "Zhrnúť Text", "description": "Poskytne stručné zhrnutie daného textu."}, "extractKeywords": {"title": "Extrakt Kľúčových Slov", "description": "Identifikuje hlavné kľúčové slová z textu."}, "explainConcept": {"title": "Vysvetliť Koncept", "description": "Získa jednoduché vysvetlenie konceptu."}}, "notes": {"noNotes": "Zatiaľ žiadne uložené poznámky. Odpovede od AI Asistenta môžu byť uložené tu.", "promptUsedLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> Prompt:", "contextProvidedLabel": "Poskytnutý Kontext:", "aiOutputLabel": "AI Výstup:", "createMarkdownFile": "Vytvoriť .md Súbor", "createMarkdownFileTooltip": "Konvertovať túto poznámku na nový Markdown súbor vo vašom projekte.", "markdownFileCreated": "Markdown súbor '{{fileName}}' vytvorený z poznámky."}}, "newAppIdea": {"pageTitle": "Generovať Nový Nápad na Appku", "regenerateButton": "Generovať Nový Nápad", "loadingMessage": "Vymýšľam nový nápad na appku...", "errorTitle": "<PERSON><PERSON><PERSON><PERSON>", "generationErrorGeneral": "Momentálne sa nepodarilo vygenerovať nápad na aplikáciu. Skúste to prosím znova.", "generationErrorInstruction": "Chyba: {{error}}. Skúste prosím generovať znova.", "generatedPromptTitle": "AI-Generovaný Nápad na Appku & Blueprint pre Vývojára", "promptInstruction": "Tento blueprint je navrhnutý na odovzdanie inej AI (napr. kódovaciemu asistentovi) na naštartovanie vývoja. Môžete ho skopírovať alebo priamo z neho vytvoriť nový projekt.", "copyPrompt": "Kopírovať Blueprint", "promptCopied": "Blueprint Skopírovaný!", "createProjectButton": "Vytvoriť Projekt z tohto Nápadu", "defaultProjectName": "Nový AI Nápad na Appku", "cannotCreateFromError": "Nie je možné vytvoriť projekt. Generovanie blueprintu zlyhalo.", "projectCreationError": "Nepodarilo sa vytvoriť projekt z tohto nápadu.", "apiKeyErrorNote": "Vytvorenie projektu je zakázané kvôli chybe API kľúča alebo generovania."}, "proAppka": {"pageTitle": "AI Projekt 'Profi' <PERSON>", "pageDescription": "Opíšte svoj nápad na aplikáciu, problém alebo požadovanú funkcionalitu. Naša AI vytvorí podrobný developerský prompt, ktorý pomôže váš nápad oživiť.", "userInputLabel": "<PERSON><PERSON><PERSON> ale<PERSON> Popis Problému:", "userInputPlaceholder": "napr. '<PERSON><PERSON><PERSON><PERSON><PERSON>, k<PERSON><PERSON> pomáha používateľom sledovať denný príjem vody a dostávať pripomienky', alebo 'Chcem vytvoriť platformu pre miestnych umelcov na predaj ich diel'. Buďte tak podrobný alebo stru<PERSON>ý, ako si <PERSON>.", "generatePromptButton": "Generovať Developerský Prompt", "loadingMessage": "Tvorím váš profesionálny blueprint aplikácie...", "errorTitle": "Generovanie Prompt<PERSON>", "errorUserInputRequired": "Prosím, najprv opíšte svoj nápad alebo problém.", "generationErrorGeneral": "Momentálne sa nepodarilo vygenerovať developerský prompt. <PERSON><PERSON><PERSON><PERSON> to prosím znova.", "generationErrorInstruction": "Chyba: {{error}}. Prosím, upresnite svoj vstup alebo skúste znova.", "generatedPromptTitle": "Vygenerovaný Developerský Prompt", "promptInstruction": "<PERSON><PERSON> prompt je pripravený pre vášho AI kódovacieho asistenta alebo na vytvorenie nového projektu.", "copyPrompt": "Kopírovať Prompt", "promptCopied": "Prompt Skopírovaný!", "createProjectButton": "Vytvoriť Projekt z tohto Promptu", "defaultProjectName": "Profi App Projekt", "cannotCreateFromError": "Nie je možné vytvoriť projekt. Generovanie promptu <PERSON>.", "projectCreationError": "Nepodarilo sa vytvoriť projekt z tohto promptu.", "apiKeyErrorNote": "Vytvorenie projektu je zakázané kvôli chybe API kľúča alebo generovania."}, "dynamicApp": {"pageTitle": "<PERSON><PERSON><PERSON><PERSON>", "pageDescription": "Vyplňte formulár nižšie a špecifikujte požiadavky na vašu aplikáciu. Naša AI vygeneruje prispôsobený developerský prompt na základe vašich vstupov.", "loadingForm": "Načítava sa konfigurácia dynamického formulára...", "generatePromptButton": "Generovať Developerský Prompt", "loadingMessage": "Generujem váš dynamický blueprint aplikácie...", "errorTitle": "Generovanie Dynamického Promptu <PERSON>lyhalo", "errorProblemDescriptionRequired": "Prosím, najprv opíšte primárny prípad použitia alebo problém na riešenie.", "generationErrorGeneral": "Momentálne sa nepodarilo vygenerovať dynamický developerský prompt. <PERSON><PERSON><PERSON><PERSON> to prosím znova.", "generationErrorInstruction": "Chyba: {{error}}. Pros<PERSON><PERSON>, skontrolujte svoje vstupy alebo skúste znova.", "generatedPromptTitle": "Vygenerovaný Dynamický Developerský Prompt", "promptInstruction": "<PERSON><PERSON> <PERSON><PERSON> prompt je založený na vašich vstupoch z formulára. Použite ho s vaším AI kódovacím asistentom alebo vytvorte nový projekt.", "copyPrompt": "Kopírovať Prompt", "promptCopied": "Prompt Skopírovaný!", "createProjectButton": "Vytvoriť Projekt z tejto Špecifikácie", "defaultProjectName": "Appka z Dynamickej Špec.", "cannotCreateFromError": "Nie je možné vytvoriť projekt. Generovanie špecifikácie zlyhalo.", "projectCreationError": "Nepodarilo sa vytvoriť projekt z tejto špecifikácie.", "apiKeyErrorNote": "Vytvorenie projektu je zakázané kvôli chybe API kľúča alebo generovania.", "form": {"sections": {"business_model": "Obchodný Model & Časová Os", "target_audience_market": "Cieľové Publikum & Trh", "core_functionality": "<PERSON><PERSON><PERSON><PERSON>ová Funkcionalita", "technical_requirements": "<PERSON><PERSON><PERSON>", "ui_ux_preferences": "UI/UX Preferencie"}, "fields": {"monetization_model": "Model Monetiz<PERSON>", "monetization_model_placeholder": "napr. <PERSON>, Freemium", "pricing_strategy": "Cenová Stratégia", "pricing_strategy_placeholder": "<PERSON><PERSON>. <PERSON>, Konkurenčná", "target_revenue": "<PERSON>ie<PERSON><PERSON><PERSON> (Voliteľné)", "target_revenue_placeholder": "napr. 10k€ MRR za 1 rok", "launch_timeline": "Požadovaný Čas Spustenia", "launch_timeline_placeholder": "napr. 1-3 mesiace", "primary_user_demographics": "Primárna <PERSON>ží<PERSON>ľov", "primary_user_demographics_placeholder": "<PERSON><PERSON><PERSON> <PERSON><PERSON> 25-40, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>", "target_geography": "Cieľová Geografia (Voliteľné)", "target_geography_placeholder": "<PERSON><PERSON>. <PERSON><PERSON>, <PERSON><PERSON>, Slovens<PERSON>", "expected_user_base": "Očakávaná Veľkosť Používateľskej Základne", "expected_user_base_placeholder": "napr. 1k-10k používateľov na začiatku", "main_competitors": "<PERSON><PERSON><PERSON><PERSON> (Voliteľné)", "main_competitors_placeholder": "<PERSON>r. <PERSON><PERSON><PERSON>t<PERSON>pp A, Riešenie B", "primary_use_case": "Primárny Prípad <PERSON> / Problém na Riešenie", "primary_use_case_placeholder": "Opíš<PERSON> hlavnú vec, ktor<PERSON> appka bude robiť alebo riešiť.", "key_features": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (oddelené čiarkou alebo zoznam)", "key_features_placeholder": "<PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ai_integration": "Úroveň AI Integrácie", "data_storage_needs": "Potreby Ukladania Dát", "platform": "<PERSON><PERSON><PERSON>ová <PERSON>/y", "api_integrations": "Požadované API Integrácie (Voliteľné)", "api_integrations_placeholder": "napr. <PERSON>e pre platby, Google Mapy pre polohu", "security_requirements": "Bezpečnostné Požiadavky", "scalability_needs": "Potreby Škálovateľnosti", "design_style": "Preferovaný Dizajnový Štýl", "accessibility_level": "Úroveň Prístupnosti (WCAG)", "animation_level": "Úroveň Animácií/Interaktivity"}, "tooltips": {"monetization_model": "Ako bude aplikácia generovať príjmy?", "pricing_strategy": "Aký prístup sa použije na stanovenie cien aplikácie alebo jej služieb?", "target_revenue": "Aké sú finančné ciele aplikácie, ak nejaké sú?", "launch_timeline": "Ako skoro plánujete spustiť prvú verziu aplikácie?", "primary_user_demographics": "Opíšte typického používateľa vašej aplikácie (vek, záujmy, profesia atď.).", "target_geography": "Kde sa budú nachádzať vaši primárni používatelia?", "expected_user_base": "Koľko aktívnych používateľov očakávate v prvom roku?", "main_competitors": "Uveďte známych konkurentov alebo podobné aplikácie.", "primary_use_case": "<PERSON><PERSON><PERSON> je naj<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ktor<PERSON> va<PERSON> a<PERSON>, ale<PERSON>, ktor<PERSON> pomáha používateľom splniť? Toto je kľúčové.", "key_features": "<PERSON><PERSON><PERSON><PERSON> hlavné funkcion<PERSON>, k<PERSON><PERSON> va<PERSON> a<PERSON>lik<PERSON> ponúkne. Oddeľte čiarkami alebo novými riadkami.", "ai_integration": "Do akej miery bude umelá inteligencia súčasťou základnej funkcionality aplikácie?", "data_storage_needs": "<PERSON><PERSON><PERSON> druh a objem dát bude aplikácia potrebovať ukladať?", "platform": "Na akých zariadeniach alebo operačných systémoch by <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>?", "api_integrations": "Uveďte akékoľvek služby tretích strán alebo API, ku ktorým sa aplikácia potrebuje pripojiť (napr. plat<PERSON><PERSON><PERSON>, soci<PERSON><PERSON>e m<PERSON>, mapov<PERSON> služby).", "security_requirements": "<PERSON><PERSON><PERSON> zabezpečenia je potrebná pre aplikáciu a jej dáta?", "scalability_needs": "<PERSON>ľko p<PERSON>žívateľov alebo aký objem dát by mala a<PERSON>lik<PERSON><PERSON> zvládnuť pri raste?", "design_style": "<PERSON><PERSON><PERSON> celkový vzhľad a dojem preferujete?", "accessibility_level": "<PERSON><PERSON><PERSON> štandardov webovej prístupnosti by mala aplik<PERSON><PERSON> spĺňať?", "animation_level": "Koľko animácií a vizuálnej spätnej väzby preferujete v používateľskom rozhraní?"}, "options": {"monetization_model": {"free": "Zadarmo (Žiadne priame príjmy)", "freemium": "Freemium (Základ <PERSON>rm<PERSON>, prémium platené)", "subscription": "Predplatné (Opakované platby)", "one_time_purchase": "Jednorazov<PERSON>", "ad_supported": "Podporovan<PERSON>", "transaction_fee": "Transakčný Poplatok / Provízia"}, "pricing_strategy": {"value_based": "Založené na hodnote", "cost_plus": "Náklady plus marža", "competitive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dynamic": "Dynamic<PERSON><PERSON>"}, "launch_timeline": {"1_2_weeks": "1-2 <PERSON><PERSON><PERSON><PERSON><PERSON> (MVP/Prototyp)", "1_3_months": "1-3 Mesiace", "3_6_months": "3-6 Mesiacov", "6_plus_months": "6+ <PERSON><PERSON><PERSON><PERSON>"}, "expected_user_base": {"lt_100_users": "< 100 pou<PERSON><PERSON><PERSON><PERSON><PERSON> (napr. intern<PERSON>, špecializovaný)", "100_1k_users": "100 - 1 000 používate<PERSON>ov", "1k_10k_users": "1 000 - 10 000 použ<PERSON><PERSON><PERSON>ov", "10k_100k_users": "10 000 - 100 000 pou<PERSON><PERSON><PERSON><PERSON>ov", "100k_plus_users": "100 000+ pou<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "ai_integration": {"none": "Žiadna alebo Minimálna", "basic_content_generation_": "Z<PERSON>lad<PERSON> (napr. jednodu<PERSON><PERSON> generovanie obs<PERSON>, sumarizácia textu)", "advanced_predictive_analytics_": "Pokročilá (napr. prediktívna analytika, komplexné odporúčania)", "core_app_fundamentally_relies_on_ai_models_": "Jadrová (Aplikácia zásadne závisí na vlastných AI modeloch)"}, "data_storage_needs": {"minimal_user_preferences_": "Minimálne (napr. používateľské preferencie, nastavenia)", "standard_user_data_documents_": "Štandardné (napr. profily <PERSON>, do<PERSON>ment<PERSON>, príspevky)", "large_media_big_data_": "Veľkokapacitné (napr. v<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, r<PERSON><PERSON><PERSON><PERSON>, Big Data)", "hybrid_local_cloud_": "Hybridné (Lokálne a Cloudové úložisko)"}, "platform": {"web_only": "Web (Desktop & Mobilné <PERSON>)", "mobile_ios_android_": "Mobilné (Natívne iOS & Android)", "desktop_windows_mac_": "Desktop (Windows, macOS, Linux)", "cross_platform_react_native_flutter_": "<PERSON>plat<PERSON><PERSON><PERSON> (napr. <PERSON>, Flutter)"}, "security_requirements": {"basic_user_auth_": "Základ<PERSON><PERSON> (Overenie používateľa)", "standard_data_encryption_regular_audits_": "Štandardné (Šifrovanie d<PERSON>, pravidelné bezpečnostné postupy)", "high_hipaa_gdpr_compliance_penetration_testing_": "Vysoké (napr. HIPAA/GDPR zhoda, penetračné testovanie)"}, "scalability_needs": {"lt1k_users": "Nízka (Menej ako 1 000 súbežných používateľov)", "1k_10k_users": "Stredná (1 000 - 10 000 súbežných používateľov)", "10k_100k_users": "Vysoká (10 000 - 100 000 súbežných používateľov)", "100k_plus_users": "<PERSON><PERSON><PERSON><PERSON> (100 000+ súbež<PERSON><PERSON><PERSON> p<PERSON>ž<PERSON>ov, globálny rozsah)"}, "design_style": {"modern_minimalist": "Moderný & Minimalistický", "classic_traditional": "Klasický & Tradičný", "dark_mode_preferred": "Preferovaný Tmavý <PERSON>žim", "visually_rich": "Vizuálne Bo<PERSON>ý & <PERSON>ú<PERSON>ý (Ilustrácie, animácie)"}, "accessibility_level": {"basic_accessibility": "Základná Prístupnosť (Sémantický HTML, klávesnicová navigácia)", "wcag_2_1_aa": "WCAG 2.1 AA Zhoda", "wcag_2_1_aaa": "WCAG 2.1 AAA Zhoda (Vysoká)"}, "animation_level": {"none": "Žiadne / Statické", "subtle_animations": "Jemné & Plynulé <PERSON>", "moderate_animations": "Mierne & Účelné <PERSON>", "extensive_animations": "Rozsiahle Animácie & Interaktívne Prvky"}}}}, "howAiWorks": {"pageTitle": "Naša AI: <PERSON><PERSON><PERSON> Supercharger", "lead": "AI Projekt Katalyzátor využíva špičkovú generatívnu AI na transformáciu vašej skúsenosti s riadením projektov. Je navrhnutý nielen na asistenciu, ale aj na aktívne spolutvorenie, strategizovanie a inšpirovanie. <PERSON> je náhľ<PERSON>, ako naša AI funguje a učí sa:", "coreCapabilitiesTitle": "Kľúčové Schopnosti AI", "capabilities": {"dataIngestion": "<strong>Inteligentné Spracovanie Dát & Predbežná Analýza:</strong> <PERSON><PERSON> vytvoríte projekt a poskytnete počiatočné informácie (názov, popis, konceptuálne súbory), AI vykoná rýchle skenovanie. Identifikuje potenciálne typy projektov, extrahuje kľúčové témy a kľúčové slová a poskytne úvodné zhrnutie. Toto pripravuje pôdu pre hlbšie porozumenie.", "semanticAnalysis": "<strong>Hĺbková Sémantická Analýza & Tvorba Znalostného Grafu:</strong> Na váš príkaz sa AI ponorí hlboko do všetkých poskytnutých projektových dát. Vykonáva sémantickú analýzu na pochopenie významu, kontextu a vzťahov vo vašich informáciách. Tento proces pomáha budovať konceptuálny znalostný graf, ktorý zdôrazňuje kľúčové entity, koncepty a ich vzájomné prepojenia.", "generativeContent": "<strong><PERSON><PERSON><PERSON><PERSON>:</strong>", "generativeContentList": ["<strong><PERSON><PERSON><PERSON><PERSON><PERSON>rí<PERSON>hy:</strong> Na základe svojej analýzy AI tvorí pútavé, \"ž<PERSON><PERSON>\" p<PERSON><PERSON><PERSON><PERSON>, ktor<PERSON> artik<PERSON> ví<PERSON>, ciele a potenciálny dopad vášho projektu.", "<strong>Vizuálna Inšpirácia:</strong> <PERSON><PERSON><PERSON>, symbolic<PERSON>é obrázky na vizuálne znázornenie podstaty vášho projektu, čím podnecuje kreativitu a poskytuje vizuálnu kotvu.", "<strong><PERSON><PERSON><PERSON>:</strong> AI pôsobí a<PERSON> kon<PERSON>, ponúka ak<PERSON><PERSON><PERSON> rady, identifikuje potenciálne riziká a príležitosti a navrhuje ďalšie kroky."], "portfolioIntelligence": "<strong>Inteligencia na Úrovni Portfólia:</strong> AI dokáže analyzovať celé vaše portfólio projektov na identifikáciu potenciálnych synergií medzi projektmi, označenie možných konfliktov a navrhnutie príležitostí na zdieľanie zdrojov alebo znalostí. Tento holistický pohľad pomáha optimalizovať vaše celkové úsilie."}, "howItLearnsTitle": "Ako sa AI Učí a Zlepšuje (Konceptuálne)", "learningDescription": "Zatiaľ čo táto demonštrácia pracuje s predtrénovanými modelmi, plne realizovaný AI Projekt Katalyzátor by zahŕňal mechanizmy pre neustále učenie a adaptáciu:", "learningMechanisms": ["<strong>Spätná Väzba z Interakcie:</strong> AI by sa učila z toho, ako interagujete s jej náv<PERSON>hm<PERSON>. Ak konzistentne prijímate určité typy rád alebo upravujete jej generovaný obsah <PERSON><PERSON><PERSON> sp<PERSON>mi, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> by s<PERSON><PERSON> bud<PERSON> výstupy tak, aby lep<PERSON><PERSON> zodpovedali vašim preferenciám a potrebám projektu.", "<strong><PERSON><PERSON><PERSON><PERSON><PERSON> Projektu (Dlhodobo):</strong> Prepojením (konceptuálne) projektových dát s ich konečnými metrikami úspechu alebo neúspechu (s súhlasom používateľa a prvoradým súkromím) by sa AI mohla naučiť identifikovať vzory, ktor<PERSON> koreluj<PERSON> s pozitívnymi výsledkami. To by jej um<PERSON><PERSON><PERSON><PERSON> zdokon<PERSON>ľovať svoje strategické rady, stávajúc sa prediktívnejšou a vnímavejšou.", "<strong><PERSON><PERSON><PERSON><PERSON>:</strong> Ak používatelia často prispôsobujú generované projektové stránky alebo vyberajú š<PERSON>k<PERSON>lóny zobrazenia pre určité typy obsahu, AI by sa mohla naučiť predvolene navrhovať vhodnejšie šablóny.", "<strong>Zdokonaľovanie Znalostí <PERSON>ch pre Doménu:</strong> Pre organizácie, ktoré nástroj rozsiahle využívajú v rámci konkrétneho odvetvia, by sa <PERSON> (s vhodným doladením alebo technikami generovania rozšíreného o vyhľadávanie) stať zdatnejšou v chápaní a generovaní obsahu špecifického pre žargón, bežné výzvy a osvedčené postupy danej domény.", "<strong>Používateľom Vedené <PERSON>:</strong> Funkcie umožňujúce používateľom explicitne hodnotiť užitočnosť návrhov AI alebo poskytovať opravnú spätnú väzbu by priamo prispievali k učebnej slučke AI, čím by sa stala personalizovanejším a efektívnejším partnerom."], "learningGoal": "Cieľom je AI, ktorá sa vyvíja spolu s vami a vašimi projektmi, stávajúc sa čoraz nepostrádateľnejším katalyzátorom inovácií a úspechu. Ide o rozšírenie ľudskej inteligencie, nie jej na<PERSON>, aby vám pomohla dosiahnuť vaše najambicióznejšie vízie.", "visionTitle": "Vízia: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Spolutvorca", "visionDescription": "Predstavte si AI, k<PERSON><PERSON> nielen anal<PERSON> d<PERSON>, ale pomáha aj pri tvorbe technických špecifikácií, brainstormingu marketingových sloganov, navrhuje <PERSON>vky kódu alebo dokonca pomáha načrtnúť prezentačné snímky. <PERSON><PERSON> je smer, ktor<PERSON><PERSON> sa uberáme – AI, ktor<PERSON> je hlboko integrovaná do kreatívnej a strategickej štruktúry va<PERSON>ej <PERSON>, čím robí vývoj projektov rýchlejším, inteligentnejším a účinnejším."}, "projectTemplate_STANDARD": "Štandardný Prehľad", "projectTemplate_VISUAL_HEAVY": "Vizuálne Bohatá Prezentácia", "projectTemplate_DATA_FOCUSED": "<PERSON><PERSON><PERSON><PERSON>prá<PERSON>", "projectTemplate_NARRATIVE_RICH": "Príbe<PERSON><PERSON>", "priority": {"high": "Vysoká", "medium": "Stredná", "low": "Nízka"}, "suggestionType": {"opportunity": "Prílež<PERSON>ť", "risk": "<PERSON><PERSON><PERSON>", "next_step": "Ďalší Krok", "synergy": "Synergia"}, "notifications": {"inputRequiredTitle": "Vyžaduje sa Vstup", "creationFailedTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "projectCreatedSuccessTitle": "<PERSON>je<PERSON>", "projectCreatedSuccessMessage": "Projekt '{{projectName}}' bol vytvorený a počiatočná analýza je dokončená!", "projectCreationFailedMessage": "Nepodarilo sa vytvoriť projekt. Skontrolujte prosím konzolu pre chyby.", "changesSavedTitle": "<PERSON><PERSON><PERSON>", "apiErrorTitle": "Chyba API", "generationFailedTitle": "<PERSON><PERSON><PERSON><PERSON>", "generationSuccessTitle": "Generovanie <PERSON>", "imageGeneratedSuccess": "{{assetName}} <PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>gene<PERSON>ý!", "imageGenerationFailed": "Generovanie {{asset}} zlyhalo: {{error}}", "noBasePromptTitle": "<PERSON>e je <PERSON>ž<PERSON>é <PERSON>ť Alternatívy", "noAlternativesFoundTitle": "Nenašli sa Žiadne Alternatívy", "updateSuccessTitle": "Aktualizácia Úspešná", "imageAlternativeSelected": "Alternatívny obrázok bol vybraný a aplikovaný.", "logoGenerationSuccessTitle": "Logo Vygenerované!", "logoAppliedDetail": "Nové logo pre '{{brandName}}' bolo v<PERSON><PERSON><PERSON> a aplikované.", "logoGenerationFailed": "Generovanie loga zlyhalo: {{error}}", "aiDevSpecGenerated": "AI Špecifikácia pre Vývojárov '{{fileName}}' bola vygenerovaná a pridaná k súborom projektu.", "blogPostGenerated": "Blogový Príspevok '{{fileName}}' bol vygenerov<PERSON>ý a pridaný k súborom projektu.", "landingPageGenerated": "Landing Page '{{fileName}}' bola vygenerovaná a pridaná k súborom projektu.", "landingPageSpecGenerated": "Špecifikácia Landing Page '{{fileName}}' bola vygenerovaná a pridaná k súborom projektu.", "specGeneratedTitle": "Špecifikácia Vygenerovaná", "noteSavedSuccessTitle": "Poznámka Uložená", "fileCreatedSuccessTitle": "<PERSON><PERSON><PERSON>", "aiAssistedFormFillTitle": "AI Asistencia Dokončená", "aiAssistedFormFillSuccess": "Polia formulára boli vyplnené návrhmi od AI.", "aiAssistedFormFillErrorTitle": "AI Asistencia <PERSON>", "fileDeletedTitle": "<PERSON><PERSON><PERSON>", "fileDeletedSuccess": "Súbor bol úspešne v<PERSON>aný.", "fileDuplicatedTitle": "<PERSON><PERSON><PERSON>", "fileDuplicatedSuccess": "<PERSON><PERSON><PERSON> '{{fileName}}' bol <PERSON><PERSON><PERSON><PERSON><PERSON>.", "fileUpdatedTitle": "<PERSON><PERSON><PERSON>", "fileUpdatedSuccess": "<PERSON><PERSON><PERSON> s<PERSON>u bol úspešne aktualizovaný.", "defaultTitleError": "Chyba", "fileOperationFailed": "Operácia so súborom zlyhala. Skontrolujte konzolu pre detaily.", "editNotSupportedTitle": "Úprava Nepodporovaná", "editNotSupportedMessage": "<PERSON>ria<PERSON> úpra<PERSON> nie je podporovaná pre typ súboru '{{fileType}}'.", "copySuccessTitle": "<PERSON><PERSON><PERSON>", "copySuccessMessage": "<PERSON><PERSON><PERSON> s<PERSON> '{{fileName}}' bol skopírovaný do s<PERSON>.", "copyErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>lyhalo", "copyNotPossibleTitle": "<PERSON>e je možn<PERSON> k<PERSON>", "copyNoContent": "Tento súbor nemá žiadny obsah na skopírovanie.", "confirmDuplicateFile": "<PERSON><PERSON><PERSON><PERSON>te duplikovať súbor '{{fileName}}'?", "confirmDeleteFile": "<PERSON><PERSON><PERSON> chcete vymazať súbor '{{fileName}}'?", "missingRequiredField": "<PERSON><PERSON><PERSON><PERSON>, v<PERSON><PERSON><PERSON><PERSON><PERSON> povinné pole: {{fieldName}}."}, "fileType": {"pdf": "PDF Dokument", "text": "Textový Dokument", "markdown": "Markdown Dokument", "json": "JSON Dáta", "png": "Obrázok (PNG)", "jpeg": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (JPEG)", "html": "HTML Dokument", "all": "Všetky Súbory", "document": "Dokumenty", "image": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "data": "<PERSON><PERSON><PERSON><PERSON>"}, "filterSortPanel": {"searchByName": "Hľadať podľa Názvu/Kľúčového slova", "searchPlaceholder": "Zadajte hľadaný výraz...", "filterByStatus": "Filtrovať podľa Stavu", "allStatuses": "Všetky Stavy", "filterByFavorite": "Filtrovať podľa Obľúbených", "all": "<PERSON><PERSON><PERSON><PERSON>", "yes": "Á<PERSON>", "no": "<PERSON><PERSON>", "sortBy": {"label": "Zoradiť Podľa", "newestFirst": "<PERSON><PERSON><PERSON> (Najnovšie Prvé)", "oldestFirst": "<PERSON><PERSON><PERSON> (Najstaršie Prvé)", "nameAsc": "Názov (A-Z)", "nameDesc": "Názov (Z-A)", "recentlyUpdated": "Naposledy Aktívne"}}, "quickViewModal": {"initialAiAnalysis": "Počiatočná AI Analýza", "strategicInsightsSummary": "<PERSON>hr<PERSON><PERSON>", "totalSuggestions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "goToFullProject": "Prejsť na Stránku Projektu"}, "logoGeneration": {"modalTitle": "AI Generá<PERSON>", "aiAssistedBriefButton": "AI Asistent Brífu", "generateButton": "Generovať Logo", "generatingMessage": "Generujem vaše jedinečné logo...", "aiBriefFailedTitle": "Asistencia AI pre <PERSON><PERSON><PERSON><PERSON>", "aiBriefFailed": "Nepodarilo sa získať asistenciu AI pre logo bríf: {{error}}", "errorBriefRequired": "Prosím, vyplňte všetky povinné polia brífu pred generovaním.", "form": {"brandName": {"label": "Názov Značky", "tooltip": "Názov vášho projektu alebo značky pre logo.", "placeholder": "<PERSON>r. <PERSON><PERSON><PERSON><PERSON><PERSON>"}, "industry": {"label": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "P<PERSON><PERSON><PERSON> alebo do<PERSON>, do ktorej váš projekt patrí.", "placeholder": "napr. Technológie, Zdravotníctvo, Vzdelávanie"}, "targetAudience": {"label": "Cieľové Publikum", "tooltip": "Opíšte va<PERSON>ich primárnych zákazníkov alebo používateľov.", "placeholder": "<PERSON>r. <PERSON><PERSON><PERSON>, <PERSON><PERSON>"}, "style": {"label": "Štýl <PERSON>", "tooltip": "Vyberte celkový estetický štýl vášho loga.", "options": {"modern": "Moderný", "vintage": "Vintage", "minimalist": "Minimalistický", "playful": "<PERSON><PERSON><PERSON><PERSON>", "luxury": "Luxusný", "traditional": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "futuristic": "Futuristický", "organic": "Organický", "other": "Iný (Špecifikujte)"}}, "styleOther": {"label": "<PERSON><PERSON>", "tooltip": "<PERSON>k ste v<PERSON> '<PERSON><PERSON>' <PERSON>, prosím špecifikujte.", "placeholder": "Opíšte jedinečnú preferenciu štýlu"}, "preferredColors": {"label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tooltip": "Uveďte preferované farby alebo farebnú paletu. Môžete použiť názvy alebo HEX kódy.", "placeholder": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>born<PERSON> alebo #FF5733, #33FF57"}, "avoidColors": {"label": "Nechať AI vybrať farby / Nemám silné preferencie", "tooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ak <PERSON>te, aby AI navrhla farby, alebo ak nemáte špecifické farebné preferencie, ktorým sa treba vyhnúť."}, "mood": {"label": "Nálada/Pocit na Vyjadrenie", "tooltip": "Akú emóciu alebo dojem by malo <PERSON> evo<PERSON>?", "options": {"trustworthy": "Dôveryhodný", "energetic": "Energický", "calm": "<PERSON><PERSON><PERSON><PERSON>", "innovative": "Inovatívny", "professional": "Profesionálny", "playful_mood": "<PERSON><PERSON><PERSON><PERSON>", "luxury_mood": "Luxusný", "natural": "Prirodzený/Organický", "other_mood": "Iný (Špecifikujte)"}}, "moodOther": {"label": "<PERSON><PERSON>", "tooltip": "Ak ste v<PERSON>bra<PERSON> '<PERSON>ú<PERSON>, prosím špecifikujte.", "placeholder": "Opíšte požadovanú náladu"}, "specificElements": {"label": "Špecifické <PERSON>v<PERSON>/Symboly (Voliteľné)", "tooltip": "Akékoľvek š<PERSON><PERSON><PERSON>, tvary alebo <PERSON>, ktor<PERSON> ch<PERSON>te zahrnúť alebo ktoré majú inšpirovať logo.", "placeholder": "<PERSON>r. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, preple<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON> 'K'"}, "avoidElements": {"label": "<PERSON><PERSON><PERSON><PERSON>, Ktorým sa Treba Vyhnúť (Voliteľné)", "tooltip": "Akékoľvek špecific<PERSON><PERSON>, tvary alebo <PERSON>, ktor<PERSON><PERSON> sa treba vyhnúť.", "placeholder": "napr. Vyhn<PERSON><PERSON> sa h<PERSON>zdam, žiadne zvieratá, udržať abstraktné"}}}, "keywords": {"visual": "vizuálny", "design": "<PERSON><PERSON><PERSON>", "image": "obrázok", "data": "<PERSON><PERSON><PERSON>", "report": "spr<PERSON><PERSON>", "analysis": "<PERSON><PERSON><PERSON>", "story": "príbeh", "narrative": "na<PERSON><PERSON><PERSON>", "software": "<PERSON><PERSON><PERSON><PERSON>", "develop": "vývoj", "app": "aplikácia", "creative": "kreatívny", "market": "marketing", "research": "výskum", "synergy": "synergia", "supports": "podporuje", "conflict": "konflikt", "blocks": "blokuje", "uses": "p<PERSON>ž<PERSON>va", "requires": "vyžaduje", "creates": "tvor<PERSON>", "produces": "produkuje"}, "serviceMessages": {"apiKeyMissingError": "Chyba API Kľúča", "apiKeyMissingErrorSummary": "API kľúč pre Gemini nie je nak<PERSON>gu<PERSON>. Prosím, nastavte ho pre použitie AI funkcií.", "apiQuotaExceededErrorTitle": "Prekročená API Kvóta", "apiQuotaExceededErrorDetail": "Požiadavku nebolo mož<PERSON><PERSON>, preto<PERSON><PERSON> bola prekročená API kvóta. Skontrolujte svoj Gemini API plán alebo skúste neskôr. Pôvodná chyba: {{error}}", "parseError": "Nepodarilo sa spracovať odpoveď AI.", "initialAnalysisError": "Počiatočná Analýza Zlyhala", "initialAnalysisErrorSummary": "Nepodarilo sa získať počiatočnú analýzu od AI: {{error}}", "imageGenerationError": "Generovanie Obrázku Zlyhalo", "deepAnalysisDefaultNarrative": "Hĺbkovú analýzu nebolo možné vykonať kvôli problému. Uistite sa, že API kľúč je platný a skúste znova.", "deepAnalysisDefaultSuggestionTitle": "Analýza Nekompletná", "deepAnalysisDefaultSuggestionDescription": "Strategické návrhy vyžadujú úspešnú hĺbkovú analýzu.", "knowledgeGraphParseErrorSummary": "Dáta znalostného grafu sa nepodarilo spracovať z odpovede AI.", "suggestionsParseErrorTitle": "Chyba Spracovania Návrhov", "suggestionsParseErrorDescription": "Strategické návrhy sa nepodarilo spracovať.", "deepAnalysisFailedError": "Hĺbková analýza zlyhala: {{error}}", "portfolioAnalysisAPIFail": "Volanie API pre analýzu portfólia zlyhalo: {{error}}", "assistantExecutionError": "Vykonanie AI Asistenta zlyhalo: {{error}}", "appIdeaGenerationError": "Generovanie nápadu na aplikáciu zlyhalo: {{error}}", "unknownError": "Vyskytla sa neznáma chyba."}}