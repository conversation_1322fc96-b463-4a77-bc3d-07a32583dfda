
export const OPTIMIZED_APP_IDEA_META_PROMPT = `
You are an expert in modern application development and AI integration. Your task is to generate a compelling and feasible application idea for a developer. Focus on innovative concepts, clear use cases, and practical implementation considerations for a developer.
The entire output, including all section titles and content, MUST be in {{outputLanguage}}.

Include the following sections:
- **Application Name:** (A catchy and relevant name in {{outputLanguage}})
- **Problem Statement:** (Clearly define the problem the application solves, in {{outputLanguage}})
- **Solution Overview:** (Briefly describe how the application solves the problem, in {{outputLanguage}})
- **Target Audience:** (Who will use this application? In {{outputLanguage}})
- **Key Features:** (List the primary functionalities, in {{outputLanguage}})
- **AI Integration (Specifics):** (Detail how AI will be used, e.g., NLP for sentiment analysis, machine learning for recommendations, generative AI for content. Be specific about the AI model's role. In {{outputLanguage}}, technical terms can remain in English if no direct translation exists or is commonly used.)
- **Monetization Model (Optional):** (How could this app potentially generate revenue? In {{outputLanguage}})
- **Technical Considerations:** (Suggest core technologies (frontend, backend, database) and any unique technical challenges or opportunities. In {{outputLanguage}}, technical terms remain in English.)
- **Deployment Strategy (Optional):** (Basic ideas for deployment. In {{outputLanguage}})
- **Market Opportunity:** (Why is this a good idea now? In {{outputLanguage}})
- **Next Steps for a Developer:** (What should a developer do to get started? In {{outputLanguage}})

Format the output clearly with bold headings (using markdown). The entire response must be ONLY this prompt.
`;

export const META_PROMPT_FOR_UNIVERSAL_APP_CREATION = `
You are an expert AI application architect. Based on the user's provided project idea or problem statement, generate a comprehensive developer prompt for building a new application. The prompt should be structured logically and provide enough detail for an AI developer to start implementation.
The entire output, including all section titles and content, MUST be in {{outputLanguage}}.

**User's Idea/Problem (Do not translate this input, use as is):** "{userInput}"

**Output Structure (All content for these sections must be in {{outputLanguage}}):**
- **Project Title:** (A concise title for the project, in {{outputLanguage}})
- **Executive Summary:** (A brief, high-level overview of the application, in {{outputLanguage}})
- **Problem Solved:** (Detail the specific problem the application addresses, in {{outputLanguage}})
- **Target Users:** (Describe the primary and secondary user groups, in {{outputLanguage}})
- **Core Functionality (MVP):** (List the absolute minimum set of features required for the first version, in {{outputLanguage}})
- **Technical Stack Recommendation:** (Suggest a modern and efficient technology stack (e.g., frontend framework, backend language/framework, database, cloud platform). Justify choices briefly. Technical terms can remain in English.)
- **AI Integration Details:** (Specify exactly how AI will be leveraged. Which AI capabilities (e.g., NLP, computer vision, generative AI, predictive analytics) will be used and for what purpose? In {{outputLanguage}}, technical terms can remain in English.)
- **Monetization Strategy:** (Briefly outline potential revenue streams, in {{outputLanguage}})
- **User Interface (UI) / User Experience (UX) Guidelines:** (Provide high-level design principles (e.g., "clean and minimalist," "visually rich and interactive," "mobile-first"). In {{outputLanguage}})
- **Acceptance Criteria (Example):** (List 2-3 examples of concrete criteria for a key feature, in {{outputLanguage}})
- **Deployment Considerations:** (Basic thoughts on how the app will be deployed. In {{outputLanguage}})
- **Future Enhancements (Post-MVP):** (Ideas for later development phases, in {{outputLanguage}})

Ensure all sections are well-defined and provide actionable insights for an AI developer. The entire response must be ONLY this prompt content.
`;

export const META_PROMPT_FOR_DYNAMIC_APP_PROMPT_GENERATION = `
You are an expert AI application architect and prompt engineer.
Your overall goal is to generate a detailed developer prompt for a new application based on user specifications.
The entire output developer prompt, including all section titles and content, MUST be in {{outputLanguage}}.

User's Detailed Specifications (Do not translate these keys/values, use their meaning to inform the {{outputLanguage}} prompt):
{{formDataString}}

Now, generate the developer prompt. Structure it clearly with the following sections, ensuring all content within these sections is in {{outputLanguage}}:
- **Application Name:** (Invent a creative and suitable name in {{outputLanguage}} based on the specs)
- **Problem Statement:** (Clearly define the problem the application solves, in {{outputLanguage}})
- **Solution Overview:** (Briefly describe how the application solves the problem, in {{outputLanguage}})
- **Target Audience:** (Describe the primary users based on specs, in {{outputLanguage}})
- **Key Features (MVP):** (List specific, actionable features derived from user input, in {{outputLanguage}})
- **AI Integration Details:** (Explain how AI will be used, specific models or techniques if appropriate, in {{outputLanguage}}. Technical terms can remain in English if no common {{outputLanguage}} equivalent.)
- **Monetization Model:** (Suggest a model based on user input, in {{outputLanguage}})
- **Technical Stack & Architecture Considerations:** (Recommend frontend, backend, database, cloud platform based on specs. Discuss any specific architectural choices. Technical terms in English.)
- **UI/UX Design Principles:** (High-level guidelines based on user preferences, in {{outputLanguage}})
- **Deployment Strategy Outline:** (Basic deployment ideas, in {{outputLanguage}})
- **Critical Success Factors:** (List 2-3 key factors for the app's success, in {{outputLanguage}})
- **Potential Challenges & Mitigations:** (Identify 1-2 potential challenges and suggest mitigations, in {{outputLanguage}})

The entire response MUST be ONLY the generated developer prompt content. No extra commentary.
`;

export const AI_AGENT_SPECIFICATION_GENERATION_META_PROMPT = `
You are an expert AI Technical Writer and System Analyst. Your task is to generate a detailed technical specification document for the provided application blueprint. This specification is intended to be used by an AI developer agent to build the application.
The entire output MUST be in Markdown format and all descriptive text, headings, and explanations MUST be in {{outputLanguage}}. Technical terms like API method names, data types, or library names can remain in English if standard or if no direct common translation exists.

Application Blueprint/Description:
---
{{blueprintContent}}
---

Instructions for Generating the AI Agent Specification:
1.  **Overall Structure:** Organize the document logically. Use Markdown headings (H2, H3, H4) for sections and sub-sections.
2.  **Target Audience:** Remember this document is for an AI agent. Prioritize clarity, explicitness, and structure over conversational prose.
3.  **Content Requirements (ensure all generated text is in {{outputLanguage}} unless specified):**
    *   **1. Overview:**
        *   **1.1. Application Purpose:** Briefly state the main goal and value proposition.
        *   **1.2. Core Problem Solved:** What primary problem does this app address?
        *   **1.3. Target Users/Systems:** Who or what will interact with this application?
    *   **2. High-Level Architecture (Conceptual):**
        *   Describe the main components (e.g., Frontend, Backend API, Database, AI Model Service).
        *   Briefly outline their responsibilities and interactions.
        *   (Optional) Suggest a basic technology stack if discernible or sensible (e.g., React Frontend, Node.js/Python Backend, PostgreSQL DB). Technical names in English.
    *   **3. Data Models / Schemas:**
        *   For each key data entity, define its properties, data types (e.g., string, number, boolean, array, object), and any constraints or relationships. Use a clear, structured format (e.g., Markdown tables or bullet points).
        *   Example Entity: User
            *   \`userId\`: string (UUID, primary_key)
            *   \`username\`: string (unique, required)
            *   \`email\`: string (unique, required, format: email)
            *   \`createdAt\`: datetime (timestamp)
    *   **4. API Endpoint Specifications (if applicable, e.g., for backend services):**
        *   For each endpoint:
            *   Method (GET, POST, PUT, DELETE)
            *   Path (e.g., \`/users\`, \`/posts/{postId}\`)
            *   Description (what it does)
            *   Request Parameters (path, query, body - with data types and if required)
            *   Success Response (status code, example body with data types)
            *   Error Responses (status codes, example error messages)
        *   Use a clear, structured format (e.g., sub-sections for each endpoint).
    *   **5. Core Logic & Algorithms:**
        *   For critical functionalities, describe the step-by-step logic or algorithm the AI agent should implement. Use bullet points or numbered lists. Be explicit.
        *   If AI models are involved (as per blueprint), specify their inputs, expected outputs, and how they integrate into the workflow.
    *   **6. UI Components & Interactions (if a frontend is implied):**
        *   List key UI components (e.g., UserLoginScreen, ProjectDashboardCard, DataUploadForm).
        *   For each component:
            *   Purpose/Description
            *   Key Props (inputs it receives, with types)
            *   Main States it manages
            *   Primary User Interactions it handles (e.g., "On button click, calls X API endpoint")
    *   **7. Non-Functional Requirements (Inferred or Standard):**
        *   Briefly mention aspects like:
            *   Performance considerations (e.g., "API responses should be <500ms")
            *   Security basics (e.g., "Input validation is required for all API endpoints", "User authentication via JWT")
            *   Error handling (e.g., "Graceful error messages for users", "Log critical errors on backend")
    *   **8. Assumptions & Constraints:**
        *   List any assumptions made while generating this spec, or any constraints mentioned in the blueprint.
4.  **Format**: Strictly Markdown.

Your response MUST be ONLY this Markdown formatted specification document. Do not include any other text before or after it.
`;
