
export const GENERIC_ASSET_GENERATION_META_PROMPT = `
You are a specialized AI assistant for generating professional project assets.
Your task is to create a "{{assetTypeDisplay}}" for the project described below, strictly following the provided specifications.
The entire output text, including headings and content, MUST be in {{outputLanguage}}. Technical terms can remain in English if standard.

Project Context:
---
Project Name: {{projectName}}
Project Description: {{projectDescription}}
Project Industry: {{projectIndustry}}
Project Target Audience: {{projectTargetAudience}}
Project Unique Selling Proposition (USP): {{projectUSP}}
Additional Project Information (e.g., from forms, other AI analyses):
{{additionalContext}}
---

Output Specifications for "{{assetTypeDisplay}}":
---
{{outputSpecifications}}
---

CRITICAL: Your response MUST be ONLY the requested "{{assetTypeDisplay}}" content in the specified format (e.g., Markdown, HTML). Do not include any other text, commentary, or introductions before or after the asset content.
If generating Markdown, use standard Markdown syntax.
If generating HTML, ensure it's a complete, valid HTML document starting with <!DOCTYPE html> and includes lang="{{htmlLangTag}}".
`;

export const AI_DEVELOPER_SPEC_OUTPUT_SPECS = `
Format: Markdown

Structure:
## 1. Overview
   - 1.1. Application Purpose (Derived from Project Name: {{appName}}, Main Purpose: {{mainPurpose}})
   - 1.2. Core Problem Solved
   - 1.3. Target Users/Systems (From: {{targetAudience}})
## 2. High-Level Architecture
   - Main Components (e.g., Frontend, Backend API, Database, AI Model Service)
   - Responsibilities & Interactions
   - Tech Stack Suggestion (From: {{techStackHighLevel}}, Architecture Type: {{architectureType}})
## 3. Key Features & User Stories
   - List based on: {{coreFeatures}}
   - For each feature, provide brief user stories or use cases.
## 4. Data Models / Schemas (Conceptual)
   - Identify key data entities based on features and purpose.
   - For each entity, list primary attributes and their conceptual types.
## 5. API Endpoint Ideas (Conceptual - if applicable)
   - Suggest key API endpoints if a backend is implied.
   - Method, Path, Brief Description.
## 6. UI/UX High-Level Concepts
   - Describe expected look and feel (From: {{uiUxHighLevel}})
   - List key screens or interfaces.
## 7. Non-Functional Requirements
   - Critical Requirements (From: {{criticalRequirements}})
   - Other Considerations (e.g., Basic Security, Performance, Scalability Ideas)
## 8. Implementation & Deployment Notes
   - Considerations for CI/CD, Hosting, Monitoring (From: {{implementationDeployment}})
## 9. Other Requirements
   - Testing, Documentation, Maintenance (From: {{otherRequirements}})
`;

export const BLOG_POST_OUTPUT_SPECS = `
Format: Markdown

Structure:
# [Catchy Title for Blog Post about {{topic}} in {{outputLanguage}}]

## Introduction (1-2 paragraphs)
   - Hook the reader. Introduce the project/topic: {{topic}}.
   - Briefly state the post's goal: {{postGoal}}.

## [Relevant Section Title 1 in {{outputLanguage}}]
   - Elaborate on key messages: {{keyMessages}}.
   - Discuss problem/solution or core aspects of {{topic}}.

## [Relevant Section Title 2 in {{outputLanguage}}]
   - Highlight benefits or unique aspects.
   - Incorporate preferred keywords naturally: {{preferredKeywords}}.

## [Relevant Section Title 3 (Optional) in {{outputLanguage}}]
   - Provide examples, use cases, or further details.
   - Target Audience: {{targetAudienceBlog}}. Tone: {{tone}}.

## Conclusion (1 paragraph)
   - Summarize key takeaways.
   - Call to Action: {{ctaText}} (Link: {{ctaLink}})

SEO Considerations:
- Meta Title: (Suggest a meta title based on {{topic}} and keywords)
- Meta Description: (Suggest a brief meta description)
- Suggested Tags/Categories: (Suggest 2-3 relevant tags)

Word Count Aim: {{postLength}} (short: ~300, medium: ~700, long: ~1200 words)
Post Structure Hint: {{postStructure}}
`;

export const LANDING_PAGE_CONTENT_AND_VISUAL_SPEC_OUTPUT_SPECS = `
Format: Markdown (This will be a specification document. The HTML itself will be a separate generation step if requested by the user later or handled by a different mechanism based on this spec).

## Part 1: Landing Page Content Specification

### 1. Overall Page Details
   - **Page Name/Title:** {{pageName}}
   - **Main Purpose:** {{mainPurposePage}}
   - **Target Audience:** {{targetAudiencePage}}
   - **Key Messages & USP:** {{keyMessagesUSP}}
   - **Primary Call to Action (CTA):**
      - Text: {{ctaTextPage}}
      - Link/Goal: {{ctaLinkPage}}
   - **SEO Keywords:** {{seoKeywords}}
   - **Meta Title Suggestion:** {{metaTitle}}
   - **Meta Description Suggestion:** {{metaDescription}}

### 2. Content by Section (Based on {{requiredSections}})
   *(For each section listed in 'requiredSections', generate the following. Use hints from 'sectionContentHints' if available.)*

   **Section: [Section Name e.g., Hero]**
   - **Headline:** (Suggest a compelling headline in {{outputLanguage}})
   - **Sub-headline/Body Text:** (Suggest content, approximately 2-4 sentences in {{outputLanguage}})
   - **Image/Visual Idea:** (Describe the type of image that would fit here, e.g., "Inspiring photo of diverse team collaborating", "Abstract graphic representing data flow")
   - **CTA (if applicable to this section):** (Suggest text and link/goal if different from primary CTA)

   *(Repeat for all sections in 'requiredSections': e.g., Benefits, How It Works, Features, Testimonials, FAQ, Pricing, Contact)*

### 3. Navigation Menu Content
   - Suggested Items: {{navMenuContent}} (e.g., "Features, Pricing, About Us, Contact")

### 4. Footer Content
   - Suggested Items: {{footerContent}} (e.g., "© {{currentYear}} {{pageName}}. All rights reserved. Privacy Policy. Terms of Service.")

## Part 2: Visual & Design Specification

### 1. Design Style & Mood
   - **Overall Style:** {{designStyle}} (e.g., modern, minimalist, luxury)
   - **Visual Tone:** (Infer from project context, e.g., "professional and trustworthy", "innovative and energetic", "calm and reassuring")

### 2. Color Palette
   - **Primary Color:** {{primaryColor}} (If HEX, use it. If descriptive, suggest a HEX. If blank, AI suggest based on style/mood.)
   - **Secondary Color:** {{secondaryColor}} (If HEX, use it. If descriptive, suggest a HEX. If blank, AI suggest.)
   - **Accent Color(s):** (AI suggest 1-2 accent colors that complement primary/secondary.)
   - **Background Colors:** (Suggest light/dark theme background colors.)

### 3. Typography
   - **Preference:** {{typographyPreference}} (serif, sans-serif, mixed)
   - **Headline Font Suggestion:** (AI suggest a specific font or font family, e.g., "Montserrat", "Open Sans")
   - **Body Font Suggestion:** (AI suggest a specific font or font family, e.g., "Roboto", "Lora")
   - **Justification:** Brief reason for font choices based on style/mood.

### 4. Imagery Style (General Guidance)
   - **Overall Image Mood:** (e.g., "Bright and optimistic", "Focused and professional", "Abstract and conceptual")
   - **Types of Images:** (e.g., "High-quality stock photos of people", "Custom illustrations", "Product mockups", "Data visualizations")
   - **Unsplash Search Terms (Examples for specific sections):**
      - For Hero: (Suggest 2-3 search terms for Unsplash, e.g., "team working", "city skyline future")
      - For Benefits: (Suggest 2-3 search terms, e.g., "happy customer", "growth chart")

### 5. Animations & Microinteractions
   - **Preference:** {{animationPreference}} (none, subtle, dynamic)
   - **Suggestions (if not 'none'):**
      - On Scroll: (e.g., "Fade-in for sections", "Parallax background effect")
      - Hover Effects: (e.g., "Buttons slightly scale up", "Links subtly change color")
      - Microinteractions: (e.g., "Loading spinners for async actions", "Input field validation feedback")

### 6. Layout & Structure Notes
   - **Overall Layout:** (e.g., "Single column for mobile, two-column for desktop where appropriate", "Full-width hero section")
   - **Spacing:** (e.g., "Generous white space for a clean look", "Compact spacing for information density")
`;

export const LOGO_GENERATION_AI_PROMPT_TEMPLATE = `
GENERATE A PROFESSIONAL LOGO. Output a high-quality, clean, vector-style logo suitable for digital use, isolated on a pure white or transparent background.

**MANDATORY INSTRUCTIONS FOR AI (Follow these strictly):**
1.  **Primary Focus:** The logo design itself.
2.  **Style:** Vector art, sharp edges, clean lines. Flat design or subtle gradients are acceptable if they enhance a modern feel.
3.  **Background:** Ensure the logo is on a SOLID WHITE (#FFFFFF) or TRANSPARENT background for easy use. No textures, patterns, or complex scenes as background.
4.  **Simplicity & Versatility:** The logo must be recognizable and effective at small sizes (e.g., favicon) and large sizes. Avoid overly intricate details that would be lost when scaled down.
5.  **Output:** The image should be a direct representation of the logo.

**DETAILED LOGO BRIEF (Use this information to design the logo):**

[BRAND NAME]: {{brandName}}

[LOGO TYPE]: {{logoType}}
(This should be intelligently inferred if not directly provided. Examples: Monogram (initials), Wordmark (full brand name stylized), Pictorial Mark (icon/symbol), Abstract Mark, Combination Mark (text + symbol), Emblem.)

[BUSINESS CONTEXT]:
Industry: {{industry}}
Target Audience: {{targetAudience}}
Additional Business Insights (derived from project context): {{businessInsights}}

[STYLE & AESTHETICS]: {{style}}
{{styleOther}}
(Elaborate with: {{styleEnhancements}}. For example, if "modern", add terms like "sleek, contemporary, geometric". If "vintage", add "classic, retro, handcrafted feel".)

[INTELLIGENT COLOR PALETTE]:
User Preferred Colors: {{preferredColors}}
{{#if avoidColors}}User explicitly stated they don't know or have no preference for colors. You MUST select a strategically sound color palette based on the industry, target audience, and desired mood. Provide 2-3 primary colors (HEX codes) and 1-2 accent colors (HEX codes). Justify your choice briefly.{{/if}}
{{#unless avoidColors}}If user provided colors, use them as primary inspiration. You can suggest complementary or accent colors. If the provided colors are problematic (e.g., clash, poor contrast), gently suggest alternatives or a refined palette. Provide HEX codes. Justify briefly.{{/unless}}
Derived Palette & Justification: {{derivedColorsAndJustification}}

[SYMBOLISM & KEY ELEMENTS]:
User-specified elements: {{specificElements}}
Core Business Essence (derived from project context): {{businessEssenceSymbolism}}
(Focus on unique, memorable symbols. Consider abstract representations. If the brand name is short and distinctive, consider incorporating it into the symbol if it makes sense visually.)

[MOOD/EMOTION TO CONVEY]: {{mood}}
{{moodOther}}
(Expand based on industry and target audience. E.g., if "trustworthy" for finance, add "secure, stable, professional". If "innovative" for tech, add "dynamic, forward-thinking, cutting-edge".)
Expanded Mood: {{expandedMood}}

[TYPOGRAPHY (If Wordmark or Combination Mark)]:
Suggested Font Style (derived from overall style): {{fontStyleSuggestion}} (e.g., "Clean sans-serif", "Elegant serif", "Custom modern script", "Bold geometric display font").
Consider legibility and brand personality.

[SMART EXCLUSIONS (Elements to Avoid)]:
User-specified exclusions: {{avoidElements}}
Industry Clichés to Avoid (AI-derived): {{industryClichesToAvoid}}
Project-Specific Exclusions (AI-derived based on negative context in project data, if any): {{projectSpecificExclusions}}

**FINAL TECHNICAL REQUIREMENTS (Reiterate for AI):**
- Vector style, high resolution feel.
- Clean, sharp lines.
- **SOLID WHITE OR TRANSPARENT BACKGROUND IS CRITICAL.**
- Suitable for digital (web, app) and print media.
- Scalable from favicon to large formats.
- AVOID COMPLEX 3D RENDERING, PHOTOREALISM, OR DETAILED ILLUSTRATIONS unless specifically part of a "Pictorial Mark" and the style allows it.

Focus on creating a distinctive, professional, and memorable logo based on this comprehensive brief.
Generate only the image.
`;

export const DOCUMENTATION_GENERATION_META_PROMPT = `
You are an AI Technical Writer tasked with creating a user-friendly documentation outline for an application based on its blueprint.
The entire output MUST be in Markdown format, and all descriptive text, headings, and explanations MUST be in {{outputLanguage}}.

Application Blueprint:
---
{{blueprintContent}}
---

Generate a documentation outline with the following sections. Ensure content is tailored to the application described in the blueprint.
1.  **Introduction**
    *   Purpose of the Application
    *   Target Audience
    *   Key Features Overview
2.  **Getting Started**
    *   System Requirements (if applicable)
    *   Installation/Setup Guide (high-level steps)
    *   First Launch & Basic Configuration
3.  **Core Functionality**
    *   (For each major feature identified in the blueprint, create a subsection)
    *   Feature X:
        *   How to use Feature X
        *   Expected inputs/outputs
        *   Tips and best practices
4.  **Advanced Features (if applicable)**
    *   (Detail any advanced or less common features)
5.  **Troubleshooting**
    *   Common Issues and Solutions
    *   Error Messages Explained
6.  **FAQ (Frequently Asked Questions)**
    *   (List 3-5 potential FAQs and their answers)
7.  **Support & Contact Information**

The response must be ONLY this Markdown formatted documentation outline. No other text.
`;

export const BLOG_POST_GENERATION_META_PROMPT = `
You are an AI Content Writer. Generate a blog post based on the provided project information.
The entire output MUST be in Markdown format, and all text MUST be in {{outputLanguage}}.

Project Information:
---
{{projectInfo}}
---

Blog Post Requirements:
-   **Title:** Create a catchy and relevant title for the blog post.
-   **Introduction:** (1-2 paragraphs) Briefly introduce the project/topic and its importance.
-   **Main Body:** (3-5 paragraphs) Discuss key aspects, benefits, or features of the project. Use clear language.
-   **Conclusion:** (1 paragraph) Summarize and provide a call to action or final thought.
-   **Tone:** Engaging and informative.
-   **SEO:** Naturally incorporate relevant keywords if discernible from the project info.

The response must be ONLY this Markdown formatted blog post. No other text.
`;

export const LANDING_PAGE_HTML_GENERATION_META_PROMPT = `
You are an AI Web Developer. Generate the HTML structure for a single-page landing page based on the provided project information.
The entire output MUST be a valid HTML5 document, and all user-visible text MUST be in {{outputLanguage}}.
The HTML should be styled using Tailwind CSS utility classes for a modern and clean look. Include placeholders for images and detailed content where appropriate.

Project Information:
---
{{projectInfo}}
---

Landing Page Requirements:
-   **Structure:** Include a header (with app name), a hero section (headline, sub-headline, CTA), a features section, a "how it works" or benefits section, and a simple footer.
-   **Content:** Use placeholders like "[App Name]", "[Catchy Headline]", "[Feature 1 Description]", etc. All placeholder text visible to the user must be in {{outputLanguage}}.
-   **Styling:** Use Tailwind CSS classes for layout, typography, colors, and spacing. Aim for a professional and appealing design.
-   **Responsiveness:** Ensure the layout is responsive.
-   **Images:** Use placeholder image links (e.g., from Pexels, Unsplash, or a service like placeholder.com) or descriptive alt text. Image dimensions can be suggested (e.g., \`https://via.placeholder.com/800x400\`).
-   **Accessibility:** Include basic accessibility considerations (e.g., alt text for images, semantic HTML).
-   **Output:** A single, complete HTML file string. Start with \`<!DOCTYPE html>\` and include \`<html lang="{{htmlLangTag}}">\`.

The response must be ONLY this HTML content. No other text or markdown.
`;
