
import { FormFieldDefinition } from '../components/DynamicAppPage'; // Assuming type is exported or defined appropriately

export interface FormSectionConfig {
  key: string; // Translation key for the section title
  fields: FormFieldDefinition[];
}

// This structure is derived from the descriptive META_PROMPT_FOR_DYNAMIC_APP_TEMPLATE
// All 'labelKey', 'tooltipKey', 'placeholderKey', and 'options[].labelKey' MUST correspond to keys in translation.json files.
// 'name' should be UPPERCASE_SNAKE_CASE and 'options[].value' should be lowercase_snake_case.

export const DYNAMIC_APP_FORM_SECTIONS_CONFIG: FormSectionConfig[] = [
  {
    key: 'dynamicApp.form.sections.business_model',
    fields: [
      {
        name: 'MONETIZATION_MODEL',
        labelKey: 'dynamicApp.form.fields.monetization_model',
        type: 'select',
        tooltipKey: 'dynamicApp.form.tooltips.monetization_model',
        options: [
          { value: 'free', labelKey: 'dynamicApp.form.options.monetization_model.free' },
          { value: 'freemium', labelKey: 'dynamicApp.form.options.monetization_model.freemium' },
          { value: 'subscription', labelKey: 'dynamicApp.form.options.monetization_model.subscription' },
          { value: 'one_time_purchase', labelKey: 'dynamicApp.form.options.monetization_model.one_time_purchase' },
          { value: 'ad_supported', labelKey: 'dynamicApp.form.options.monetization_model.ad_supported' },
          { value: 'transaction_fee', labelKey: 'dynamicApp.form.options.monetization_model.transaction_fee' }
        ],
        defaultValue: 'freemium',
        sectionKey: 'dynamicApp.form.sections.business_model',
      },
      {
        name: 'PRICING_STRATEGY',
        labelKey: 'dynamicApp.form.fields.pricing_strategy',
        type: 'select',
        tooltipKey: 'dynamicApp.form.tooltips.pricing_strategy',
        options: [
          { value: 'value_based', labelKey: 'dynamicApp.form.options.pricing_strategy.value_based' },
          { value: 'cost_plus', labelKey: 'dynamicApp.form.options.pricing_strategy.cost_plus' },
          { value: 'competitive', labelKey: 'dynamicApp.form.options.pricing_strategy.competitive' },
          { value: 'dynamic', labelKey: 'dynamicApp.form.options.pricing_strategy.dynamic' }
        ],
        defaultValue: 'value_based',
        sectionKey: 'dynamicApp.form.sections.business_model',
      },
      {
        name: 'TARGET_REVENUE',
        labelKey: 'dynamicApp.form.fields.target_revenue',
        type: 'text',
        tooltipKey: 'dynamicApp.form.tooltips.target_revenue',
        placeholderKey: 'dynamicApp.form.fields.target_revenue_placeholder',
        defaultValue: '',
        sectionKey: 'dynamicApp.form.sections.business_model',
      },
      {
        name: 'LAUNCH_TIMELINE',
        labelKey: 'dynamicApp.form.fields.launch_timeline',
        type: 'select',
        tooltipKey: 'dynamicApp.form.tooltips.launch_timeline',
        options: [
          { value: '1_2_weeks', labelKey: 'dynamicApp.form.options.launch_timeline.1_2_weeks' },
          { value: '1_3_months', labelKey: 'dynamicApp.form.options.launch_timeline.1_3_months' },
          { value: '3_6_months', labelKey: 'dynamicApp.form.options.launch_timeline.3_6_months' },
          { value: '6_plus_months', labelKey: 'dynamicApp.form.options.launch_timeline.6_plus_months' }
        ],
        defaultValue: '1_3_months',
        sectionKey: 'dynamicApp.form.sections.business_model',
      }
    ]
  },
  {
    key: 'dynamicApp.form.sections.target_audience_market',
    fields: [
      {
        name: 'PRIMARY_USER_DEMOGRAPHICS',
        labelKey: 'dynamicApp.form.fields.primary_user_demographics',
        type: 'textarea',
        tooltipKey: 'dynamicApp.form.tooltips.primary_user_demographics',
        placeholderKey: 'dynamicApp.form.fields.primary_user_demographics_placeholder',
        defaultValue: '',
        sectionKey: 'dynamicApp.form.sections.target_audience_market',
      },
      {
        name: 'TARGET_GEOGRAPHY',
        labelKey: 'dynamicApp.form.fields.target_geography',
        type: 'text',
        tooltipKey: 'dynamicApp.form.tooltips.target_geography',
        placeholderKey: 'dynamicApp.form.fields.target_geography_placeholder',
        defaultValue: '',
        sectionKey: 'dynamicApp.form.sections.target_audience_market',
      },
      {
        name: 'EXPECTED_USER_BASE',
        labelKey: 'dynamicApp.form.fields.expected_user_base',
        type: 'select',
        tooltipKey: 'dynamicApp.form.tooltips.expected_user_base',
        options: [
          { value: 'lt_100_users', labelKey: 'dynamicApp.form.options.expected_user_base.lt_100_users' },
          { value: '100_1k_users', labelKey: 'dynamicApp.form.options.expected_user_base.100_1k_users' },
          { value: '1k_10k_users', labelKey: 'dynamicApp.form.options.expected_user_base.1k_10k_users' },
          { value: '10k_100k_users', labelKey: 'dynamicApp.form.options.expected_user_base.10k_100k_users' },
          { value: '100k_plus_users', labelKey: 'dynamicApp.form.options.expected_user_base.100k_plus_users' }
        ],
        defaultValue: '1k_10k_users',
        sectionKey: 'dynamicApp.form.sections.target_audience_market',
      },
      {
        name: 'MAIN_COMPETITORS',
        labelKey: 'dynamicApp.form.fields.main_competitors',
        type: 'text',
        tooltipKey: 'dynamicApp.form.tooltips.main_competitors',
        placeholderKey: 'dynamicApp.form.fields.main_competitors_placeholder',
        defaultValue: '',
        sectionKey: 'dynamicApp.form.sections.target_audience_market',
      }
    ]
  },
  {
    key: 'dynamicApp.form.sections.core_functionality',
    fields: [
      {
        name: 'PRIMARY_USE_CASE',
        labelKey: 'dynamicApp.form.fields.primary_use_case',
        type: 'textarea',
        tooltipKey: 'dynamicApp.form.tooltips.primary_use_case',
        placeholderKey: 'dynamicApp.form.fields.primary_use_case_placeholder',
        defaultValue: '',
        required: true,
        sectionKey: 'dynamicApp.form.sections.core_functionality',
      },
      {
        name: 'KEY_FEATURES',
        labelKey: 'dynamicApp.form.fields.key_features',
        type: 'textarea',
        tooltipKey: 'dynamicApp.form.tooltips.key_features',
        placeholderKey: 'dynamicApp.form.fields.key_features_placeholder',
        defaultValue: '',
        sectionKey: 'dynamicApp.form.sections.core_functionality',
      },
      {
        name: 'AI_INTEGRATION',
        labelKey: 'dynamicApp.form.fields.ai_integration',
        type: 'select',
        tooltipKey: 'dynamicApp.form.tooltips.ai_integration',
        options: [
          { value: 'none', labelKey: 'dynamicApp.form.options.ai_integration.none' },
          { value: 'basic_content_generation_', labelKey: 'dynamicApp.form.options.ai_integration.basic_content_generation_' },
          { value: 'advanced_predictive_analytics_', labelKey: 'dynamicApp.form.options.ai_integration.advanced_predictive_analytics_' },
          { value: 'core_app_fundamentally_relies_on_ai_models_', labelKey: 'dynamicApp.form.options.ai_integration.core_app_fundamentally_relies_on_ai_models_' }
        ],
        defaultValue: 'none',
        sectionKey: 'dynamicApp.form.sections.core_functionality',
      },
      {
        name: 'DATA_STORAGE_NEEDS',
        labelKey: 'dynamicApp.form.fields.data_storage_needs',
        type: 'select',
        tooltipKey: 'dynamicApp.form.tooltips.data_storage_needs',
        options: [
          { value: 'minimal_user_preferences_', labelKey: 'dynamicApp.form.options.data_storage_needs.minimal_user_preferences_' },
          { value: 'standard_user_data_documents_', labelKey: 'dynamicApp.form.options.data_storage_needs.standard_user_data_documents_' },
          { value: 'large_media_big_data_', labelKey: 'dynamicApp.form.options.data_storage_needs.large_media_big_data_' },
          { value: 'hybrid_local_cloud_', labelKey: 'dynamicApp.form.options.data_storage_needs.hybrid_local_cloud_' }
        ],
        defaultValue: 'standard_user_data_documents_',
        sectionKey: 'dynamicApp.form.sections.core_functionality',
      }
    ]
  },
  {
    key: 'dynamicApp.form.sections.technical_requirements',
    fields: [
      {
        name: 'PLATFORM',
        labelKey: 'dynamicApp.form.fields.platform',
        type: 'select',
        tooltipKey: 'dynamicApp.form.tooltips.platform',
        options: [
          { value: 'web_only', labelKey: 'dynamicApp.form.options.platform.web_only' },
          { value: 'mobile_ios_android_', labelKey: 'dynamicApp.form.options.platform.mobile_ios_android_' },
          { value: 'desktop_windows_mac_', labelKey: 'dynamicApp.form.options.platform.desktop_windows_mac_' },
          { value: 'cross_platform_react_native_flutter_', labelKey: 'dynamicApp.form.options.platform.cross_platform_react_native_flutter_' }
        ],
        defaultValue: 'web_only',
        sectionKey: 'dynamicApp.form.sections.technical_requirements',
      },
      {
        name: 'API_INTEGRATIONS',
        labelKey: 'dynamicApp.form.fields.api_integrations',
        type: 'textarea',
        tooltipKey: 'dynamicApp.form.tooltips.api_integrations',
        placeholderKey: 'dynamicApp.form.fields.api_integrations_placeholder',
        defaultValue: '',
        sectionKey: 'dynamicApp.form.sections.technical_requirements',
      },
      {
        name: 'SECURITY_REQUIREMENTS',
        labelKey: 'dynamicApp.form.fields.security_requirements',
        type: 'select',
        tooltipKey: 'dynamicApp.form.tooltips.security_requirements',
        options: [
          { value: 'basic_user_auth_', labelKey: 'dynamicApp.form.options.security_requirements.basic_user_auth_' },
          { value: 'standard_data_encryption_regular_audits_', labelKey: 'dynamicApp.form.options.security_requirements.standard_data_encryption_regular_audits_' },
          { value: 'high_hipaa_gdpr_compliance_penetration_testing_', labelKey: 'dynamicApp.form.options.security_requirements.high_hipaa_gdpr_compliance_penetration_testing_' }
        ],
        defaultValue: 'standard_data_encryption_regular_audits_',
        sectionKey: 'dynamicApp.form.sections.technical_requirements',
      },
      {
        name: 'SCALABILITY_NEEDS',
        labelKey: 'dynamicApp.form.fields.scalability_needs',
        type: 'select',
        tooltipKey: 'dynamicApp.form.tooltips.scalability_needs',
        options: [
            { value: 'lt1k_users', labelKey: 'dynamicApp.form.options.scalability_needs.lt1k_users'},
            { value: '1k_10k_users', labelKey: 'dynamicApp.form.options.scalability_needs.1k_10k_users'},
            { value: '10k_100k_users', labelKey: 'dynamicApp.form.options.scalability_needs.10k_100k_users'},
            { value: '100k_plus_users', labelKey: 'dynamicApp.form.options.scalability_needs.100k_plus_users'}
        ],
        defaultValue: '1k_10k_users',
        sectionKey: 'dynamicApp.form.sections.technical_requirements',
      }
    ]
  },
  {
    key: 'dynamicApp.form.sections.ui_ux_preferences',
    fields: [
      {
        name: 'DESIGN_STYLE',
        labelKey: 'dynamicApp.form.fields.design_style',
        type: 'select',
        tooltipKey: 'dynamicApp.form.tooltips.design_style',
        options: [
          { value: 'modern_minimalist', labelKey: 'dynamicApp.form.options.design_style.modern_minimalist' },
          { value: 'classic_traditional', labelKey: 'dynamicApp.form.options.design_style.classic_traditional' },
          { value: 'dark_mode_preferred', labelKey: 'dynamicApp.form.options.design_style.dark_mode_preferred' },
          { value: 'visually_rich', labelKey: 'dynamicApp.form.options.design_style.visually_rich' }
        ],
        defaultValue: 'modern_minimalist',
        sectionKey: 'dynamicApp.form.sections.ui_ux_preferences',
      },
      {
        name: 'ACCESSIBILITY_LEVEL',
        labelKey: 'dynamicApp.form.fields.accessibility_level',
        type: 'select',
        tooltipKey: 'dynamicApp.form.tooltips.accessibility_level',
        options: [
          { value: 'basic_accessibility', labelKey: 'dynamicApp.form.options.accessibility_level.basic_accessibility' },
          { value: 'wcag_2_1_aa', labelKey: 'dynamicApp.form.options.accessibility_level.wcag_2_1_aa' },
          { value: 'wcag_2_1_aaa', labelKey: 'dynamicApp.form.options.accessibility_level.wcag_2_1_aaa' }
        ],
        defaultValue: 'wcag_2_1_aa',
        sectionKey: 'dynamicApp.form.sections.ui_ux_preferences',
      },
      {
        name: 'ANIMATION_LEVEL',
        labelKey: 'dynamicApp.form.fields.animation_level',
        type: 'select',
        tooltipKey: 'dynamicApp.form.tooltips.animation_level',
        options: [
          { value: 'none', labelKey: 'dynamicApp.form.options.animation_level.none' },
          { value: 'subtle_animations', labelKey: 'dynamicApp.form.options.animation_level.subtle_animations' },
          { value: 'moderate_animations', labelKey: 'dynamicApp.form.options.animation_level.moderate_animations' },
          { value: 'extensive_animations', labelKey: 'dynamicApp.form.options.animation_level.extensive_animations' }
        ],
        defaultValue: 'subtle_animations',
        sectionKey: 'dynamicApp.form.sections.ui_ux_preferences',
      }
    ]
  }
  // Note: The full list of fields from META_PROMPT_FOR_DYNAMIC_APP_TEMPLATE should be converted here.
  // This example shows a subset for brevity.
];
