
export const FILE_TYPES_SUPPORTED = [
  { nameKey: 'fileType.pdf', mime: 'application/pdf', extension: '.pdf' },
  { nameKey: 'fileType.text', mime: 'text/plain', extension: '.txt' },
  { nameKey: 'fileType.markdown', mime: 'text/markdown', extension: '.md' },
  { nameKey: 'fileType.json', mime: 'application/json', extension: '.json' },
  { nameKey: 'fileType.png', mime: 'image/png', extension: '.png' },
  { nameKey: 'fileType.jpeg', mime: 'image/jpeg', extension: '.jpg' },
  { nameKey: 'fileType.html', mime: 'text/html', extension: '.html' }
];

export const FILE_TYPES_SUPPORTED_FILTER = [
  { nameKey: 'fileType.all', mime: '*', extension: '*' },
  { nameKey: 'fileType.document', mime: 'application/pdf,text/plain,text/markdown', extension: '.pdf,.txt,.md' },
  { nameKey: 'fileType.image', mime: 'image/png,image/jpeg', extension: '.png,.jpg,.jpeg' },
  { nameKey: 'fileType.data', mime: 'application/json', extension: '.json' },
  { nameKey: 'fileType.html', mime: 'text/html', extension: '.html'}
];
