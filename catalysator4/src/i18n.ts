
import i18next from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Async function to fetch translation files
async function fetchTranslations() {
  try {
    const [enResponse, skResponse] = await Promise.all([
      fetch('/src/locales/en/translation.json'), // Absolute path from server root
      fetch('/src/locales/sk/translation.json')  // Absolute path from server root
    ]);

    if (!enResponse.ok) {
      throw new Error(`Failed to fetch enTranslations: ${enResponse.statusText}`);
    }
    if (!skResponse.ok) {
      throw new Error(`Failed to fetch skTranslations: ${skResponse.statusText}`);
    }

    const enTranslations = await enResponse.json();
    const skTranslations = await skResponse.json();

    return {
      en: { translation: enTranslations },
      sk: { translation: skTranslations },
    };
  } catch (error) {
    console.error("Error loading translation files:", error);
    // Fallback to empty translations in case of an error
    // This will ensure the app can still run, albeit without translations,
    // and the specific error will be in the console.
    return {
      en: { translation: { /* Consider adding a few critical fallback keys here if needed */ } },
      sk: { translation: { /* Consider adding a few critical fallback keys here if needed */ } },
    };
  }
}

// Export a promise that resolves when i18next is initialized
export const i18nPromise = fetchTranslations().then(resources => {
  return i18next
    .use(initReactI18next)
    .use(LanguageDetector)
    .init({
      resources,
      lng: 'sk', // Set Slovak as the default language
      fallbackLng: 'en',
      interpolation: {
        escapeValue: false, // React already protects from XSS
      },
      detection: {
        order: ['localStorage', 'navigator', 'htmlTag'],
        caches: ['localStorage'],
      },
      debug: false, // Set to true for debugging
    });
}).catch(error => {
  console.error("Major error during i18next initialization:", error);
  // This catch block is to prevent unhandled promise rejection if fetchTranslations itself fails critically
  // or if .init() fails in a way not caught internally by i18next.
  // The app might not render correctly if this happens, but it prevents a crash.
  // The console error from fetchTranslations should provide more details.
});

export default i18next;
