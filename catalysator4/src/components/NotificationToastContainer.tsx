
import React, { useEffect } from 'react';
import { useNotifications } from '../contexts/NotificationContext';
import { NotificationMessage, NotificationType } from '../types';
import { ICONS } from '../config'; // Updated import

const NotificationToast: React.FC<{ notification: NotificationMessage; onDismiss: (id: string) => void }> = ({ notification, onDismiss }) => {
  useEffect(() => {
    const timer = setTimeout(() => {
      onDismiss(notification.id);
    }, notification.duration || 7000); // Increased default duration for important messages like quota errors

    return () => clearTimeout(timer);
  }, [notification, onDismiss]);

  const getIcon = (type: NotificationType) => {
    switch (type) {
      case 'success':
        return ICONS.CHECK_CIRCLE && React.cloneElement(ICONS.CHECK_CIRCLE, { className: "w-6 h-6 text-status-success" });
      case 'error':
        return ICONS.EXCLAMATION_TRIANGLE && React.cloneElement(ICONS.EXCLAMATION_TRIANGLE, { className: "w-6 h-6 text-status-error" });
      case 'info':
        return ICONS.INFO_CIRCLE_SOLID && React.cloneElement(ICONS.INFO_CIRCLE_SOLID, { className: "w-6 h-6 text-status-info" });
      case 'warning':
        return ICONS.WARNING && React.cloneElement(ICONS.WARNING, { className: "w-6 h-6 text-status-warning" });
      case 'api_quota_exceeded':
        return ICONS.SHIELD_EXCLAMATION && React.cloneElement(ICONS.SHIELD_EXCLAMATION, { className: "w-6 h-6 text-status-error" }); // Specific icon for quota
      default:
        return null;
    }
  };

  const getBorderColor = (type: NotificationType) => {
    switch (type) {
      case 'success': return 'border-status-success';
      case 'error': return 'border-status-error';
      case 'info': return 'border-status-info';
      case 'warning': return 'border-status-warning';
      case 'api_quota_exceeded': return 'border-status-error';
      default: return 'border-neutral-30';
    }
  };
  
  const getTitleColor = (type: NotificationType) => {
    switch (type) {
      case 'success': return 'text-status-success';
      case 'error': return 'text-status-error';
      case 'info': return 'text-status-info';
      case 'warning': return 'text-status-warning';
      case 'api_quota_exceeded': return 'text-status-error font-bold';
      default: return 'text-neutral-800';
    }
  };


  return (
    <div 
        className={`bg-base-100 rounded-lg shadow-2xl p-4 border-l-4 ${getBorderColor(notification.type)} flex items-start space-x-3 transition-all duration-300 ease-in-out transform animate-toast-in`}
        role="alert"
        aria-live="assertive"
        aria-atomic="true"
    >
      <div className="flex-shrink-0">
        {getIcon(notification.type)}
      </div>
      <div className="flex-1">
        {notification.title && <h4 className={`text-sm font-semibold ${getTitleColor(notification.type)}`}>{notification.title}</h4>}
        <p className="text-sm text-neutral-700">{notification.message}</p>
      </div>
      <button
        onClick={() => onDismiss(notification.id)}
        className="p-1 text-neutral-500 hover:text-neutral-700 rounded-full hover:bg-neutral-100 focus:outline-none focus:ring-1 focus:ring-neutral-400"
        aria-label="Dismiss notification"
      >
        {ICONS.X_MARK && React.cloneElement(ICONS.X_MARK, {className: "w-4 h-4"})}
      </button>
    </div>
  );
};

const NotificationToastContainer: React.FC = () => {
  const { notifications, removeNotification } = useNotifications();

  if (!notifications.length) return null;

  return (
    <div className="fixed top-4 right-4 z-[2000] w-full max-w-sm space-y-3">
      {notifications.map(notification => (
        <NotificationToast key={notification.id} notification={notification} onDismiss={removeNotification} />
      ))}
    </div>
  );
};

export default NotificationToastContainer;
