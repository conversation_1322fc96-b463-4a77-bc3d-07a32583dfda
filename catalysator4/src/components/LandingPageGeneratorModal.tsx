
import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Project, LandingPageFormData } from '../types';
import { useProjects } from '../hooks/useProjects';
import { useNotifications } from '../contexts/NotificationContext';
import { ICONS } from '../config';
import LoadingSpinner from './LoadingSpinner';

interface LandingPageGeneratorModalProps {
  isOpen: boolean;
  onClose: () => void;
  project: Project | null;
  onSubmit: (formData: LandingPageFormData) => Promise<void>;
  isLoading: boolean; // Loading state controlled by the parent (ProjectPage)
}

const landingPageFormFields: Array<{ name: keyof LandingPageFormData; labelKey: string; type: 'text' | 'textarea' | 'select'; options?: Array<{value: string, labelKey: string}>; placeholderKey?: string; required?: boolean }> = [
  { name: 'pageName', labelKey: 'projectPage.assetGenerator.modal.landingPage.pageNameLabel', type: 'text', placeholderKey: 'projectPage.assetGenerator.modal.landingPage.pageNamePlaceholder', required: true },
  { name: 'mainPurposePage', labelKey: 'projectPage.assetGenerator.modal.landingPage.mainPurposePageLabel', type: 'textarea', placeholderKey: 'projectPage.assetGenerator.modal.landingPage.mainPurposePagePlaceholder', required: true },
  { name: 'targetAudiencePage', labelKey: 'projectPage.assetGenerator.modal.landingPage.targetAudiencePageLabel', type: 'textarea', placeholderKey: 'projectPage.assetGenerator.modal.landingPage.targetAudiencePagePlaceholder' },
  { name: 'keyMessagesUSP', labelKey: 'projectPage.assetGenerator.modal.landingPage.keyMessagesUSPLabel', type: 'textarea', placeholderKey: 'projectPage.assetGenerator.modal.landingPage.keyMessagesUSPPlaceholder', required: true },
  { 
    name: 'designStyle', 
    labelKey: 'projectPage.assetGenerator.modal.landingPage.designStyleLabel', 
    type: 'select',
    options: [
      { value: '', labelKey: 'common.selectOption' },
      { value: 'modern', labelKey: 'projectPage.assetGenerator.modal.landingPage.styleOptionsLp.modern' },
      { value: 'minimalist', labelKey: 'projectPage.assetGenerator.modal.landingPage.styleOptionsLp.minimalist' },
      { value: 'luxury', labelKey: 'projectPage.assetGenerator.modal.landingPage.styleOptionsLp.luxury' },
      { value: 'playful_lp', labelKey: 'projectPage.assetGenerator.modal.landingPage.styleOptionsLp.playful_lp' },
      { value: 'other_style_lp', labelKey: 'common.other' }
    ]
  },
  { name: 'primaryColor', labelKey: 'projectPage.assetGenerator.modal.landingPage.primaryColorLabel', type: 'text', placeholderKey: 'projectPage.assetGenerator.modal.landingPage.primaryColorPlaceholder' },
  { name: 'secondaryColor', labelKey: 'projectPage.assetGenerator.modal.landingPage.secondaryColorLabel', type: 'text', placeholderKey: 'projectPage.assetGenerator.modal.landingPage.secondaryColorPlaceholder' },
  { 
    name: 'typographyPreference', 
    labelKey: 'projectPage.assetGenerator.modal.landingPage.typographyPreferenceLabel', 
    type: 'select',
    options: [
      { value: '', labelKey: 'common.selectOption' },
      { value: 'serif', labelKey: 'projectPage.assetGenerator.modal.landingPage.typoOptions.serif' },
      { value: 'sans-serif', labelKey: 'projectPage.assetGenerator.modal.landingPage.typoOptions.sans-serif' },
      { value: 'mixed', labelKey: 'projectPage.assetGenerator.modal.landingPage.typoOptions.mixed' },
      { value: 'other_typo', labelKey: 'common.other' }
    ]
  },
  { name: 'requiredSections', labelKey: 'projectPage.assetGenerator.modal.landingPage.requiredSectionsLabel', type: 'textarea', placeholderKey: 'projectPage.assetGenerator.modal.landingPage.requiredSectionsPlaceholder' },
  { name: 'sectionContentHints', labelKey: 'projectPage.assetGenerator.modal.landingPage.sectionContentHintsLabel', type: 'textarea', placeholderKey: 'projectPage.assetGenerator.modal.landingPage.sectionContentHintsPlaceholder' },
  { name: 'ctaTextPage', labelKey: 'projectPage.assetGenerator.modal.landingPage.ctaTextPageLabel', type: 'text', placeholderKey: 'projectPage.assetGenerator.modal.landingPage.ctaTextPagePlaceholder' },
  { name: 'ctaLinkPage', labelKey: 'projectPage.assetGenerator.modal.landingPage.ctaLinkPageLabel', type: 'text', placeholderKey: 'projectPage.assetGenerator.modal.landingPage.ctaLinkPagePlaceholder' },
  { 
    name: 'animationPreference', 
    labelKey: 'projectPage.assetGenerator.modal.landingPage.animationPreferenceLabel', 
    type: 'select',
    options: [
      { value: '', labelKey: 'common.selectOption' },
      { value: 'none_anim', labelKey: 'projectPage.assetGenerator.modal.landingPage.animOptions.none_anim' },
      { value: 'subtle', labelKey: 'projectPage.assetGenerator.modal.landingPage.animOptions.subtle' },
      { value: 'dynamic', labelKey: 'projectPage.assetGenerator.modal.landingPage.animOptions.dynamic' },
    ]
  },
  { name: 'navMenuContent', labelKey: 'projectPage.assetGenerator.modal.landingPage.navMenuContentLabel', type: 'text', placeholderKey: 'projectPage.assetGenerator.modal.landingPage.navMenuContentPlaceholder' },
  { name: 'footerContent', labelKey: 'projectPage.assetGenerator.modal.landingPage.footerContentLabel', type: 'textarea', placeholderKey: 'projectPage.assetGenerator.modal.landingPage.footerContentPlaceholder' },
  { name: 'seoKeywords', labelKey: 'projectPage.assetGenerator.modal.landingPage.seoKeywordsLabel', type: 'text', placeholderKey: 'projectPage.assetGenerator.modal.landingPage.seoKeywordsPlaceholder' },
  { name: 'metaTitle', labelKey: 'projectPage.assetGenerator.modal.landingPage.metaTitleLabel', type: 'text', placeholderKey: 'projectPage.assetGenerator.modal.landingPage.metaTitlePlaceholder' },
  { name: 'metaDescription', labelKey: 'projectPage.assetGenerator.modal.landingPage.metaDescriptionLabel', type: 'textarea', placeholderKey: 'projectPage.assetGenerator.modal.landingPage.metaDescriptionPlaceholder' },
];


export const LandingPageGeneratorModal: React.FC<LandingPageGeneratorModalProps> = ({ isOpen, onClose, project, onSubmit, isLoading }) => {
  const { t } = useTranslation();
  const { getAIAssistanceForLandingPageForm, updateProjectDataCore } = useProjects();
  const { showNotification } = useNotifications();

  const getInitialFormData = (currentProject: Project | null): LandingPageFormData => {
    const year = new Date().getFullYear();
    return {
      pageName: currentProject?.name || '',
      mainPurposePage: currentProject?.description || '',
      targetAudiencePage: '',
      keyMessagesUSP: '',
      designStyle: '',
      primaryColor: '',
      secondaryColor: '',
      typographyPreference: '',
      requiredSections: ['Hero', 'Features', 'Benefits', 'CTA'], // Default array
      sectionContentHints: {}, // Default object
      ctaTextPage: '',
      ctaLinkPage: '',
      animationPreference: '',
      navMenuContent: 'Home, Features, Pricing, Contact', // Default string
      footerContent: `© ${year} ${currentProject?.name || 'Your Company'}. All rights reserved.`, // Default string
      seoKeywords: currentProject?.initialAnalysis?.keywords?.join(', ') || '',
      metaTitle: '',
      metaDescription: '',
    };
  };
  
  const [formData, setFormData] = useState<LandingPageFormData>(getInitialFormData(project));
  const [isFillingWithAI, setIsFillingWithAI] = useState(false);

  useEffect(() => {
    if (project && isOpen) {
      const initialData = getInitialFormData(project);
      setFormData(prev => ({
        ...initialData,
        ...(project.dataCore.landingPageFormData || {}), 
        requiredSections: project.dataCore.landingPageFormData?.requiredSections && project.dataCore.landingPageFormData.requiredSections.length > 0 
                          ? project.dataCore.landingPageFormData.requiredSections 
                          : initialData.requiredSections,
        sectionContentHints: project.dataCore.landingPageFormData?.sectionContentHints || initialData.sectionContentHints,
      }));
    }
  }, [project, isOpen]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    if (name === 'requiredSections') {
      setFormData(prev => ({ ...prev, [name]: value.split(',').map(s => s.trim()).filter(s => s) }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };
  
  const handleHintsChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    const hintsArray = value.split('\n');
    const hintsObject: Record<string, string> = {};
    hintsArray.forEach(line => {
        const parts = line.split(':');
        if (parts.length >= 2) {
            const key = parts[0].trim();
            const val = parts.slice(1).join(':').trim();
            if (key) hintsObject[key] = val;
        }
    });
    setFormData(prev => ({ ...prev, [name]: hintsObject }));
  };


  const handleFillWithAI = async () => {
    if (!project) return;
    setIsFillingWithAI(true);
    try {
      const aiAssistedData = await getAIAssistanceForLandingPageForm(project);
      const mergedData = {
        ...formData, 
        ...aiAssistedData, 
        requiredSections: aiAssistedData.requiredSections && Array.isArray(aiAssistedData.requiredSections) && aiAssistedData.requiredSections.length > 0
                          ? aiAssistedData.requiredSections
                          : formData.requiredSections,
        sectionContentHints: aiAssistedData.sectionContentHints && typeof aiAssistedData.sectionContentHints === 'object'
                             ? { ...formData.sectionContentHints, ...aiAssistedData.sectionContentHints } 
                             : formData.sectionContentHints,
      };
      setFormData(mergedData);
      showNotification({ type: 'success', title: t('notifications.aiAssistedFormFillTitle'), message: t('notifications.aiAssistedFormFillSuccess') });
    } catch (error: any) {
      showNotification({ type: 'error', title: t('notifications.aiAssistedFormFillErrorTitle'), message: error.message });
    } finally {
      setIsFillingWithAI(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!project) return;

    const requiredTextualFields: (keyof LandingPageFormData)[] = ['pageName', 'mainPurposePage', 'keyMessagesUSP'];
    for (const fieldName of requiredTextualFields) {
        const fieldConfig = landingPageFormFields.find(f => f.name === fieldName);
        if (!formData[fieldName] || String(formData[fieldName]).trim() === '') {
            showNotification({ 
                type: 'warning', 
                title: t('notifications.inputRequiredTitle'), 
                message: t('notifications.missingRequiredField', { fieldName: fieldConfig ? t(fieldConfig.labelKey) : fieldName }) 
            });
            return;
        }
    }
    if (!formData.requiredSections || formData.requiredSections.length === 0) {
        showNotification({ type: 'warning', title: t('notifications.inputRequiredTitle'), message: t('notifications.missingRequiredField', { fieldName: t('projectPage.assetGenerator.modal.landingPage.requiredSectionsLabel') }) });
        return;
    }

    updateProjectDataCore(project.id, { landingPageFormData: formData });
    await onSubmit(formData);
  };
  
  const commonInputClasses = "block w-full px-3 py-1.5 border border-neutral-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm text-xs bg-base-100";

  if (!isOpen) return null;
   if (!project) {
    return (
      <div className="fixed inset-0 bg-neutral-900/70 backdrop-blur-sm flex items-center justify-center z-[1000] p-4" onClick={onClose}>
        <div className="bg-base-100 p-6 rounded-xl shadow-2xl w-full max-w-md text-center" onClick={e => e.stopPropagation()}>
          <p>{t('projectPage.projectNotFound')}</p>
          <button onClick={onClose} className="btn-secondary mt-4">{t('common.close')}</button>
        </div>
      </div>
    );
  }

  return (
    <div 
        className="fixed inset-0 bg-neutral-900/70 backdrop-blur-sm flex items-center justify-center z-[1000] p-4 transition-opacity duration-300 ease-in-out"
        onClick={onClose}
        role="dialog"
        aria-modal="true"
        aria-labelledby="lp-generator-modal-title"
    >
      <div 
        className="bg-base-100 rounded-xl shadow-2xl w-full max-w-3xl max-h-[90vh] flex flex-col"
        onClick={e => e.stopPropagation()}
      >
        <div className="flex justify-between items-center p-4 sm:p-5 border-b border-neutral-200">
          <h3 id="lp-generator-modal-title" className="text-lg font-semibold text-neutral-800 flex items-center">
            {ICONS.LINK && React.cloneElement(ICONS.LINK, {className:"w-5 h-5 mr-2 text-primary"})}
            {t('projectPage.assetGenerator.modal.landingPage.title')}
          </h3>
          <button onClick={onClose} className="p-1 text-neutral-500 hover:text-error rounded-full focus-visible:ring-2 focus-visible:ring-error" aria-label={t('common.close')}>
            {ICONS.X_MARK && React.cloneElement(ICONS.X_MARK, {className: "w-5 h-5"})}
          </button>
        </div>

        <form onSubmit={handleSubmit} className="flex-grow contents">
          <div className="p-4 sm:p-5 flex-grow overflow-y-auto custom-scrollbar">
            <div className="flex justify-between items-center mb-3">
              <p className="text-xs text-neutral-600">{t('projectPage.assetGenerator.modal.landingPage.formDescription')}</p>
              <button
                type="button"
                onClick={handleFillWithAI}
                disabled={isFillingWithAI || isLoading}
                className="btn-secondary text-xs px-2 py-1 flex items-center disabled:opacity-50"
              >
                {isFillingWithAI ? <LoadingSpinner size="xs"/> : ICONS.AI_SPARKLE && React.cloneElement(ICONS.AI_SPARKLE, {className:"w-3 h-3"})}
                <span className="ml-1">{t('projectPage.assetGenerator.modal.fillWithAIButton')}</span>
              </button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-3">
              {landingPageFormFields.map(field => (
                <div key={field.name} className={field.type === 'textarea' && ['mainPurposePage', 'keyMessagesUSP', 'targetAudiencePage', 'sectionContentHints', 'footerContent', 'metaDescription'].includes(field.name) ? 'md:col-span-2' : ''}>
                  <label htmlFor={`lp_${field.name}`} className="block text-xs font-medium text-neutral-700">
                    {t(field.labelKey)} {field.required && <span className="text-error">*</span>}
                  </label>
                  {field.type === 'select' && field.options ? (
                    <select id={`lp_${field.name}`} name={field.name} value={String(formData[field.name] || '')} onChange={handleInputChange} className={commonInputClasses} required={field.required}>
                      {field.options.map(opt => <option key={opt.value} value={opt.value}>{t(opt.labelKey)}</option>)}
                    </select>
                  ) : field.type === 'textarea' ? (
                     <textarea
                        id={`lp_${field.name}`}
                        name={field.name}
                        value={
                            field.name === 'requiredSections' ? (Array.isArray(formData.requiredSections) ? formData.requiredSections.join(', ') : '') :
                            field.name === 'sectionContentHints' ? 
                                (typeof formData.sectionContentHints === 'object' ? 
                                    Object.entries(formData.sectionContentHints).map(([key, value]) => `${key}: ${value}`).join('\n') : '')
                                : String(formData[field.name] || '')
                        }
                        onChange={field.name === 'sectionContentHints' ? handleHintsChange : handleInputChange}
                        rows={field.name === 'sectionContentHints' || field.name === 'footerContent' ? 4 : 2}
                        className={commonInputClasses}
                        placeholder={field.placeholderKey ? t(field.placeholderKey) : undefined}
                        required={field.required}
                    />
                  ) : (
                    <input
                      type="text"
                      id={`lp_${field.name}`}
                      name={field.name}
                      value={String(formData[field.name] || '')}
                      onChange={handleInputChange}
                      className={commonInputClasses}
                      placeholder={field.placeholderKey ? t(field.placeholderKey) : undefined}
                      required={field.required}
                    />
                  )}
                </div>
              ))}
            </div>
          </div>

          <div className="p-4 sm:p-5 border-t border-neutral-200 flex justify-end space-x-3">
            <button type="button" onClick={onClose} className="btn-secondary px-4 py-2 text-sm">
              {t('projectPage.assetGenerator.modal.cancelButton')}
            </button>
            <button
              type="submit"
              disabled={isLoading || isFillingWithAI}
              className="btn-primary flex items-center justify-center px-4 py-2 text-sm"
            >
              {isLoading ? (
                <LoadingSpinner variant="dots" size="xs" color="text-base-100" icon={React.cloneElement(ICONS.AI_SPARKLE, {className:"w-3.5 h-3.5"})} containerClassName="flex flex-row items-center"/>
              ) : (
                ICONS.AI_SPARKLE && React.cloneElement(ICONS.AI_SPARKLE, {className:"w-4 h-4"})
              )}
              <span className="ml-2">{t('projectPage.assetGenerator.modal.generateButton')}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};