
import React, { useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useProjects } from '../hooks/useProjects';
import { useNotifications } from '../contexts/NotificationContext';
import { geminiService } from '../services/geminiService';
import { OPTIMIZED_APP_IDEA_META_PROMPT, ICONS } from '../config';
import { useDeveloperPromptGenerator, DeveloperPromptDisplayCard } from './DeveloperPromptTools'; // New import

const NewAppIdeaPage: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { showNotification } = useNotifications();
  const { addProject, isLoading: isProjectsLoadingGlobal } = useProjects();

  const {
    generatedPrompt,
    isLoading: isLoadingIdea,
    error,
    copySuccess,
    generatePrompt: fetchNewIdea,
    handleCopyPrompt,
  } = useDeveloperPromptGenerator({
    metaPromptTemplate: OPTIMIZED_APP_IDEA_META_PROMPT,
    generationServiceCall: geminiService.generateAppIdeaDeveloperPrompt,
    errorContextKey: 'newAppIdea',
  });

  useEffect(() => {
    fetchNewIdea(); // Fetch on initial load
  }, [fetchNewIdea]);

  const handleCreateProjectFromIdea = async () => {
    if (!generatedPrompt || generatedPrompt.startsWith(t('serviceMessages.apiKeyMissingErrorSummary', {lng: i18n.language})) || error) {
      showNotification({ type: 'error', title: t('notifications.creationFailedTitle'), message: t('newAppIdea.cannotCreateFromError')});
      return;
    }

    let projectName = t('newAppIdea.defaultProjectName');
    const namePatterns = [
        /^\*\*Application Name:\*\*\s*([^\n]+)/im, /^\*\*Názov aplikácie:\*\*\s*([^\n]+)/im,
        /^\*\*Project Title:\*\*\s*([^\n]+)/im, /^\*\*Názov Projektu:\*\*\s*([^\n]+)/im,
        /^Application Name:\s*([^\n]+)/im, /^Názov aplikácie:\s*([^\n]+)/im,
        /^Project Title:\s*([^\n]+)/im, /^Názov Projektu:\s*([^\n]+)/im,
    ];

    for (const pattern of namePatterns) {
        const match = generatedPrompt.match(pattern);
        if (match && match[1] && match[1].trim()) {
            projectName = match[1].trim();
            break;
        }
    }
    
    const projectDescription = generatedPrompt; 
    const newProjectId = await addProject(projectName, projectDescription, [], true); 
    if (newProjectId) {
      showNotification({ type: 'success', title: t('notifications.projectCreatedSuccessTitle'), message: t('notifications.projectCreatedSuccessMessage', { projectName }) });
      navigate(`/project/${newProjectId}`);
    } else {
      showNotification({ type: 'error', title: t('notifications.creationFailedTitle'), message: t('newAppIdea.projectCreationError')});
    }
  };

  return (
    <div className="bg-base-100 p-6 sm:p-8 rounded-lg shadow-xl">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-3">
        <h1 className="text-2xl sm:text-3xl font-bold text-primary flex items-center">
          {ICONS.WAND && React.cloneElement(ICONS.WAND, { className: "w-7 h-7 sm:w-8 sm:h-8 mr-3 text-ai-creative"})} 
          {t('newAppIdea.pageTitle')}
        </h1>
        <button
            onClick={() => fetchNewIdea()} // Use the hook's generatePrompt directly
            disabled={isLoadingIdea || isProjectsLoadingGlobal}
            className="btn-secondary inline-flex items-center px-4 py-2 border border-neutral-30 text-sm font-medium rounded-md shadow-sm hover:bg-neutral-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 self-start sm:self-center"
        >
            {ICONS.AI_SPARKLE && React.cloneElement(ICONS.AI_SPARKLE, {className: "text-accent"})}
            <span className="ml-2">{t('newAppIdea.regenerateButton')}</span>
        </button>
      </div>

      <DeveloperPromptDisplayCard
        generatedPrompt={generatedPrompt}
        isLoading={isLoadingIdea}
        error={error}
        copySuccess={copySuccess}
        onCopyPrompt={handleCopyPrompt}
        onCreateProject={handleCreateProjectFromIdea}
        isCreatingProject={isProjectsLoadingGlobal}
        titleKey="newAppIdea.generatedPromptTitle"
        instructionKey="newAppIdea.promptInstruction"
        createButtonKey="newAppIdea.createProjectButton"
        errorContextKey="newAppIdea"
      />
    </div>
  );
};

export default NewAppIdeaPage;
