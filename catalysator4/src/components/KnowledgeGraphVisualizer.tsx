
import React from 'react';
import { useTranslation } from 'react-i18next';
import { KnowledgeGraph, KnowledgeGraphElement, KnowledgeGraphRelation } from '../types';
import { ICONS } from '../config'; // Updated import

interface KnowledgeGraphVisualizerProps {
  graph?: KnowledgeGraph;
}

const KnowledgeGraphVisualizer: React.FC<KnowledgeGraphVisualizerProps> = ({ graph }) => {
  const { t } = useTranslation();

  if (!graph || (!graph.elements?.length && !graph.summary)) {
    return <p className="text-neutral-500 text-sm">{t('knowledgeGraph.notAvailable')}</p>;
  }

  return (
    <div className="space-y-4">
      {graph.summary && (
        <div className="p-3 bg-primary/5 rounded-md border border-primary/10 flex items-start">
          {ICONS.AI_SPARKLE && React.cloneElement(ICONS.AI_SPARKLE, {className: "w-4 h-4 mr-2 mt-0.5 text-primary flex-shrink-0"})}
          <p className="text-sm text-neutral-70 italic">
            <strong className="not-italic text-primary">{t('knowledgeGraph.aiSummary')}</strong> {graph.summary}
          </p>
        </div>
      )}
      
      {graph.elements && graph.elements.length > 0 && (
        <div>
          <h4 className="text-md font-semibold text-neutral mb-2">{t('knowledgeGraph.keyEntitiesAndConcepts', { count: graph.elements.length })}</h4>
          <div className="flex flex-wrap gap-2">
            {graph.elements.map((el: KnowledgeGraphElement) => (
              <span
                key={el.id}
                className={`px-3 py-1 text-xs font-semibold rounded-full shadow-sm border
                  ${el.type === 'entity' 
                    ? 'bg-ai-technical/10 text-ai-technical border-ai-technical/30' 
                    : 'bg-ai-research/10 text-ai-research border-ai-research/30'
                  }`}
                title={`${t('knowledgeGraph.typeLabel', {defaultValue: "Type"})}: ${el.type}`}
              >
                {el.label}
              </span>
            ))}
          </div>
        </div>
      )}

      {graph.relations && graph.relations.length > 0 && (
         <div>
          <h4 className="text-md font-semibold text-neutral mb-2">{t('knowledgeGraph.identifiedRelationships', { count: graph.relations.length })}</h4>
          <ul className="space-y-2 text-sm">
            {graph.relations.map((rel: KnowledgeGraphRelation, index: number) => {
              const sourceNode = graph.elements?.find(e => e.id === rel.source);
              const targetNode = graph.elements?.find(e => e.id === rel.target);
              
              let relationColor = "text-neutral-600";
              let relationBg = "bg-neutral-100";
              const relLabelLower = rel.label.toLowerCase();

              if (relLabelLower.includes(t('keywords.synergy', {lng: 'en'}).toLowerCase()) || relLabelLower.includes(t('keywords.supports', {lng: 'en'}).toLowerCase())) {
                relationColor = "text-ai-synergy"; relationBg="bg-ai-synergy/10";
              } else if (relLabelLower.includes(t('keywords.conflict', {lng: 'en'}).toLowerCase()) || relLabelLower.includes(t('keywords.blocks', {lng: 'en'}).toLowerCase())) {
                relationColor = "text-status-error"; relationBg="bg-status-error/10";
              } else if (relLabelLower.includes(t('keywords.uses', {lng: 'en'}).toLowerCase()) || relLabelLower.includes(t('keywords.requires', {lng: 'en'}).toLowerCase())) {
                relationColor = "text-ai-technical"; relationBg="bg-ai-technical/10";
              } else if (relLabelLower.includes(t('keywords.creates', {lng: 'en'}).toLowerCase()) || relLabelLower.includes(t('keywords.produces', {lng: 'en'}).toLowerCase())) {
                relationColor = "text-ai-creative"; relationBg="bg-ai-creative/10";
              }


              return (
                <li key={index} className="p-2.5 bg-base-100 rounded-md flex items-center space-x-2 flex-wrap shadow-sm border border-neutral-200">
                  <span className={`font-medium px-1.5 py-0.5 rounded text-xs ${sourceNode?.type === 'entity' ? 'bg-ai-technical/10 text-ai-technical' : 'bg-ai-research/10 text-ai-research'}`}>
                    {sourceNode?.label || rel.source}
                  </span>
                  <span className="text-neutral-400 text-lg">&rarr;</span> 
                  <span className={`italic font-medium px-1.5 py-0.5 rounded text-xs ${relationColor} ${relationBg}`}>{rel.label}</span>
                  <span className="text-neutral-400 text-lg">&rarr;</span> 
                  <span className={`font-medium px-1.5 py-0.5 rounded text-xs ${targetNode?.type === 'entity' ? 'bg-ai-technical/10 text-ai-technical' : 'bg-ai-research/10 text-ai-research'}`}>
                    {targetNode?.label || rel.target}
                  </span>
                </li>
              );
            })}
          </ul>
        </div>
      )}
      {graph.elements && graph.elements.length > 0 && (
        <p className="text-xs text-neutral-500 mt-4">
            {t('knowledgeGraph.textualRepresentationNote')}
        </p>
      )}
    </div>
  );
};

export default KnowledgeGraphVisualizer;
