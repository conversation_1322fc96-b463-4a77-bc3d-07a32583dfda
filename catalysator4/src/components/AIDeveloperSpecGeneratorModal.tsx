
import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Project, AIDevSpecFormData } from '../types';
import { useProjects } from '../hooks/useProjects';
import { useNotifications } from '../contexts/NotificationContext';
import { ICONS } from '../config'; // Updated import
import LoadingSpinner from './LoadingSpinner';

interface AIDeveloperSpecGeneratorModalProps {
  isOpen: boolean;
  onClose: () => void;
  project: Project | null;
  onSubmit: (formData: AIDevSpecFormData) => Promise<void>;
  isLoading: boolean; // Loading state controlled by the parent (ProjectPage)
}

const aiDevSpecFormFields: Array<{ name: keyof AIDevSpecFormData; labelKey: string; type: 'text' | 'textarea' | 'select'; options?: Array<{value: string, labelKey: string}>; placeholderKey?: string; required?: boolean }> = [
  { name: 'appName', labelKey: 'projectPage.assetGenerator.modal.aiDevSpec.appNameLabel', type: 'text', placeholderKey: 'projectPage.assetGenerator.modal.aiDevSpec.appNamePlaceholder', required: true },
  { name: 'mainPurpose', labelKey: 'projectPage.assetGenerator.modal.aiDevSpec.mainPurposeLabel', type: 'textarea', placeholderKey: 'projectPage.assetGenerator.modal.aiDevSpec.mainPurposePlaceholder', required: true },
  { name: 'industry', labelKey: 'projectPage.assetGenerator.modal.aiDevSpec.industryLabel', type: 'text', placeholderKey: 'projectPage.assetGenerator.modal.aiDevSpec.industryPlaceholder' },
  { name: 'targetAudience', labelKey: 'projectPage.assetGenerator.modal.aiDevSpec.targetAudienceLabel', type: 'textarea', placeholderKey: 'projectPage.assetGenerator.modal.aiDevSpec.targetAudiencePlaceholder' },
  { name: 'usp', labelKey: 'projectPage.assetGenerator.modal.aiDevSpec.uspLabel', type: 'textarea', placeholderKey: 'projectPage.assetGenerator.modal.aiDevSpec.uspPlaceholder' },
  { name: 'coreFeatures', labelKey: 'projectPage.assetGenerator.modal.aiDevSpec.coreFeaturesLabel', type: 'textarea', placeholderKey: 'projectPage.assetGenerator.modal.aiDevSpec.coreFeaturesPlaceholder', required: true },
  { name: 'criticalRequirements', labelKey: 'projectPage.assetGenerator.modal.aiDevSpec.criticalRequirementsLabel', type: 'textarea', placeholderKey: 'projectPage.assetGenerator.modal.aiDevSpec.criticalRequirementsPlaceholder' },
  { name: 'techStackHighLevel', labelKey: 'projectPage.assetGenerator.modal.aiDevSpec.techStackHighLevelLabel', type: 'textarea', placeholderKey: 'projectPage.assetGenerator.modal.aiDevSpec.techStackHighLevelPlaceholder' },
  { 
    name: 'architectureType', 
    labelKey: 'projectPage.assetGenerator.modal.aiDevSpec.architectureTypeLabel', 
    type: 'select',
    options: [
      { value: '', labelKey: 'common.selectOption' }, 
      { value: 'monolith', labelKey: 'projectPage.assetGenerator.modal.aiDevSpec.architectureOptions.monolith' },
      { value: 'microservices', labelKey: 'projectPage.assetGenerator.modal.aiDevSpec.architectureOptions.microservices' },
      { value: 'serverless', labelKey: 'projectPage.assetGenerator.modal.aiDevSpec.architectureOptions.serverless' },
      { value: 'other_arch', labelKey: 'common.other' }
    ]
  },
  { name: 'userFlows', labelKey: 'projectPage.assetGenerator.modal.aiDevSpec.userFlowsLabel', type: 'textarea', placeholderKey: 'projectPage.assetGenerator.modal.aiDevSpec.userFlowsPlaceholder' },
  { name: 'uiUxHighLevel', labelKey: 'projectPage.assetGenerator.modal.aiDevSpec.uiUxHighLevelLabel', type: 'textarea', placeholderKey: 'projectPage.assetGenerator.modal.aiDevSpec.uiUxHighLevelPlaceholder' },
  { name: 'implementationDeployment', labelKey: 'projectPage.assetGenerator.modal.aiDevSpec.implementationDeploymentLabel', type: 'textarea', placeholderKey: 'projectPage.assetGenerator.modal.aiDevSpec.implementationDeploymentPlaceholder' },
  { name: 'otherRequirements', labelKey: 'projectPage.assetGenerator.modal.aiDevSpec.otherRequirementsLabel', type: 'textarea', placeholderKey: 'projectPage.assetGenerator.modal.aiDevSpec.otherRequirementsPlaceholder' },
];


export const AIDeveloperSpecGeneratorModal: React.FC<AIDeveloperSpecGeneratorModalProps> = ({ isOpen, onClose, project, onSubmit, isLoading }) => {
  const { t } = useTranslation();
  const { getAIAssistanceForAIDevSpecForm, updateProjectDataCore } = useProjects();
  const { showNotification } = useNotifications();

  const initialFormData: AIDevSpecFormData = {
    appName: project?.name || '',
    mainPurpose: project?.description || '',
    industry: project?.initialAnalysis?.projectTypeGuess || '',
    targetAudience: '',
    usp: '',
    coreFeatures: '',
    criticalRequirements: '',
    techStackHighLevel: '',
    architectureType: '',
    userFlows: '',
    uiUxHighLevel: '',
    implementationDeployment: '',
    otherRequirements: '',
  };

  const [formData, setFormData] = useState<AIDevSpecFormData>(initialFormData);
  const [isFillingWithAI, setIsFillingWithAI] = useState(false);

  useEffect(() => {
    if (project && isOpen) { 
      setFormData(prev => ({
        ...initialFormData, 
        appName: project.name || initialFormData.appName,
        mainPurpose: project.description || initialFormData.mainPurpose,
        industry: project.initialAnalysis?.projectTypeGuess || initialFormData.industry,
        ...(project.dataCore.aiDevSpecFormData || {}) 
      }));
    }
  }, [project, isOpen]); 


  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleFillWithAI = async () => {
    if (!project) return;
    setIsFillingWithAI(true);
    try {
      const aiAssistedData = await getAIAssistanceForAIDevSpecForm(project);
      const updatedFormData = { ...formData, ...aiAssistedData };
      setFormData(updatedFormData);
      showNotification({ type: 'success', title: t('notifications.aiAssistedFormFillTitle'), message: t('notifications.aiAssistedFormFillSuccess') });
    } catch (error: any) {
      showNotification({ type: 'error', title: t('notifications.aiAssistedFormFillErrorTitle'), message: error.message });
    } finally {
      setIsFillingWithAI(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!project) return;

    const requiredTextualFields: (keyof AIDevSpecFormData)[] = ['appName', 'mainPurpose', 'coreFeatures'];
    for (const fieldName of requiredTextualFields) {
        const fieldConfig = aiDevSpecFormFields.find(f => f.name === fieldName);
        if (!formData[fieldName] || String(formData[fieldName]).trim() === '') {
            showNotification({ 
                type: 'warning', 
                title: t('notifications.inputRequiredTitle'), 
                message: t('notifications.missingRequiredField', { fieldName: fieldConfig ? t(fieldConfig.labelKey) : fieldName }) 
            });
            return;
        }
    }
    updateProjectDataCore(project.id, { aiDevSpecFormData: formData });
    await onSubmit(formData);
  };
  
  const commonInputClasses = "block w-full px-3 py-1.5 border border-neutral-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm text-xs bg-base-100";

  if (!isOpen) return null;

  if (!project) { 
    return (
      <div className="fixed inset-0 bg-neutral-900/70 backdrop-blur-sm flex items-center justify-center z-[1000] p-4" onClick={onClose}>
        <div className="bg-base-100 p-6 rounded-xl shadow-2xl w-full max-w-md text-center" onClick={e => e.stopPropagation()}>
          <p>{t('projectPage.projectNotFound')}</p>
          <button onClick={onClose} className="btn-secondary mt-4">{t('common.close')}</button>
        </div>
      </div>
    );
  }

  return (
    <div 
        className="fixed inset-0 bg-neutral-900/70 backdrop-blur-sm flex items-center justify-center z-[1000] p-4 transition-opacity duration-300 ease-in-out"
        onClick={onClose}
        role="dialog"
        aria-modal="true"
        aria-labelledby="ai-dev-spec-modal-title"
    >
      <div 
        className="bg-base-100 rounded-xl shadow-2xl w-full max-w-3xl max-h-[90vh] flex flex-col"
        onClick={e => e.stopPropagation()}
      >
        <div className="flex justify-between items-center p-4 sm:p-5 border-b border-neutral-200">
          <h3 id="ai-dev-spec-modal-title" className="text-lg font-semibold text-neutral-800 flex items-center">
            {ICONS.CPU_CHIP && React.cloneElement(ICONS.CPU_CHIP, {className:"w-5 h-5 mr-2 text-primary"})}
            {t('projectPage.assetGenerator.modal.aiDevSpec.title')}
          </h3>
          <button onClick={onClose} className="p-1 text-neutral-500 hover:text-error rounded-full focus-visible:ring-2 focus-visible:ring-error" aria-label={t('common.close')}>
            {ICONS.X_MARK && React.cloneElement(ICONS.X_MARK, {className: "w-5 h-5"})}
          </button>
        </div>

        <form onSubmit={handleSubmit} className="flex-grow contents"> 
          <div className="p-4 sm:p-5 flex-grow overflow-y-auto custom-scrollbar">
            <div className="flex justify-between items-center mb-3">
              <p className="text-xs text-neutral-600">{t('projectPage.assetGenerator.modal.aiDevSpec.formDescription')}</p>
              <button
                type="button"
                onClick={handleFillWithAI}
                disabled={isFillingWithAI || isLoading}
                className="btn-secondary text-xs px-2 py-1 flex items-center disabled:opacity-50"
              >
                {isFillingWithAI ? <LoadingSpinner size="xs"/> : ICONS.AI_SPARKLE && React.cloneElement(ICONS.AI_SPARKLE, {className:"w-3 h-3"})}
                <span className="ml-1">{t('projectPage.assetGenerator.modal.fillWithAIButton')}</span>
              </button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-3">
              {aiDevSpecFormFields.map(field => (
                <div key={field.name} className={field.type === 'textarea' && ['mainPurpose', 'coreFeatures', 'criticalRequirements', 'techStackHighLevel', 'userFlows', 'uiUxHighLevel', 'implementationDeployment', 'otherRequirements'].includes(field.name) ? 'md:col-span-2' : ''}>
                  <label htmlFor={`devSpec_${field.name}`} className="block text-xs font-medium text-neutral-700">
                    {t(field.labelKey)} {field.required && <span className="text-error">*</span>}
                  </label>
                  {field.type === 'select' && field.options ? (
                    <select id={`devSpec_${field.name}`} name={field.name} value={String(formData[field.name] || '')} onChange={handleInputChange} className={commonInputClasses} required={field.required}>
                      {field.options.map(opt => <option key={opt.value} value={opt.value}>{t(opt.labelKey)}</option>)}
                    </select>
                  ) : field.type === 'textarea' ? (
                    <textarea id={`devSpec_${field.name}`} name={field.name} value={String(formData[field.name] || '')} onChange={handleInputChange} rows={2} className={commonInputClasses} placeholder={field.placeholderKey ? t(field.placeholderKey) : undefined} required={field.required}/>
                  ) : (
                    <input type="text" id={`devSpec_${field.name}`} name={field.name} value={String(formData[field.name] || '')} onChange={handleInputChange} className={commonInputClasses} placeholder={field.placeholderKey ? t(field.placeholderKey) : undefined} required={field.required}/>
                  )}
                </div>
              ))}
            </div>
          </div>

          <div className="p-4 sm:p-5 border-t border-neutral-200 flex justify-end space-x-3">
            <button type="button" onClick={onClose} className="btn-secondary px-4 py-2 text-sm">
              {t('projectPage.assetGenerator.modal.cancelButton')}
            </button>
            <button
              type="submit"
              disabled={isLoading || isFillingWithAI}
              className="btn-primary flex items-center justify-center px-4 py-2 text-sm"
            >
              {isLoading ? (
                <LoadingSpinner variant="dots" size="xs" color="text-base-100" icon={React.cloneElement(ICONS.AI_SPARKLE, {className:"w-3.5 h-3.5"})} containerClassName="flex flex-row items-center"/>
              ) : (
                ICONS.AI_SPARKLE && React.cloneElement(ICONS.AI_SPARKLE, {className:"w-4 h-4"})
              )}
              <span className="ml-2">{t('projectPage.assetGenerator.modal.generateButton')}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};