
import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Project, LogoBriefData, LogoStyleOptionValue, LogoMoodOptionValue, GeneratedImageDetails } from '../types';
import { useProjects } from '../hooks/useProjects';
import { useNotifications } from '../contexts/NotificationContext';
import { ICONS, LOGO_BRIEF_FORM_CONFIG, LogoFormFieldDefinition } from '../config'; // Updated import
import LoadingSpinner from './LoadingSpinner';

interface LogoGenerationModalProps {
  isOpen: boolean;
  onClose: () => void;
  project: Project | null;
}

export const LogoGenerationModal: React.FC<LogoGenerationModalProps> = ({ isOpen, onClose, project }) => {
  const { t, i18n } = useTranslation();
  const {
    isLoading: isProjectsHookLoading,
    // updateProjectGeneratedImage, // Not directly used here for this fix, handled by generateAndSetProjectLogo
    fetchAIAssistedLogoBrief,
    saveLogoBriefToDataCore,
    generateAndSetProjectLogo
  } = useProjects();
  const { showNotification } = useNotifications();

  const getDefaultBriefData = useCallback((currentProject: Project | null): LogoBriefData => ({
    brandName: currentProject?.name || '',
    industry: currentProject?.initialAnalysis?.projectTypeGuess || '',
    targetAudience: '',
    style: 'modern',
    styleOther: '',
    preferredColors: '',
    avoidColors: false,
    mood: 'innovative',
    moodOther: '',
    specificElements: '',
    avoidElements: '',
  }), []);

  const [briefData, setBriefData] = useState<LogoBriefData>(getDefaultBriefData(null)); // Initialize with pure defaults
  const [isLoadingBrief, setIsLoadingBrief] = useState(false);
  const [isGeneratingLogo, setIsGeneratingLogo] = useState(false);
  const [initialBriefDataLoaded, setInitialBriefDataLoaded] = useState(false);
  const [prevProjectId, setPrevProjectId] = useState<string | null | undefined>(null);


  useEffect(() => {
    if (isOpen) {
      const currentProjectId = project?.id;
      // Load initial data if:
      // 1. Modal is newly opened and initial data hasn't been loaded yet for this session.
      // 2. The project ID has changed since the last time data was loaded.
      if (!initialBriefDataLoaded || (currentProjectId !== prevProjectId)) {
        const defaultSettingsBasedOnProject = getDefaultBriefData(project);
        const userSavedSettingsPartial = project?.dataCore.logoBrief;

        let finalBrief: LogoBriefData = { ...defaultSettingsBasedOnProject };
        if (userSavedSettingsPartial) {
          finalBrief = { ...finalBrief, ...userSavedSettingsPartial };
        }
        // Prioritize current project name/industry if no specific brief value exists
        finalBrief.brandName = userSavedSettingsPartial?.brandName || defaultSettingsBasedOnProject.brandName;
        finalBrief.industry = userSavedSettingsPartial?.industry || defaultSettingsBasedOnProject.industry;
        
        setBriefData(finalBrief);
        setInitialBriefDataLoaded(true);
        setPrevProjectId(currentProjectId);
      }
    } else {
      // Modal is closed, reset everything for next open
      setBriefData(getDefaultBriefData(null)); // Reset to pure defaults based on NO project
      setInitialBriefDataLoaded(false);
      setPrevProjectId(null); // Reset previous project ID
    }
  }, [project, isOpen, getDefaultBriefData, initialBriefDataLoaded, prevProjectId]);


  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const isCheckbox = type === 'checkbox';
    setBriefData(prev => ({
      ...prev,
      [name]: isCheckbox ? (e.target as HTMLInputElement).checked : value,
    }));
  };

  const handleAIAssistBrief = async () => {
    if (!project) return;
    setIsLoadingBrief(true);
    try {
      const aiBriefSuggestions = await fetchAIAssistedLogoBrief(project.id);
      if (aiBriefSuggestions) {
        setBriefData(prevBrief => ({
          ...prevBrief,
          ...aiBriefSuggestions // This will overwrite existing fields with AI suggestions, including empty strings
        }));
        // initialBriefDataLoaded remains true, form data is now user/AI modified
        showNotification({ type: 'success', title: t('notifications.aiAssistedFormFillTitle'), message: t('notifications.aiAssistedFormFillSuccess') });
      } else {
        showNotification({ type: 'warning', title: t('logoGeneration.aiBriefFailedTitle'), message: t('logoGeneration.aiBriefFailed', {error: 'No suggestions returned.'}) });
      }
    } catch (error: any) {
      showNotification({ type: 'error', title: t('logoGeneration.aiBriefFailedTitle'), message: t('logoGeneration.aiBriefFailed', {error: error.message}) });
    } finally {
      setIsLoadingBrief(false);
    }
  };

  const handleGenerateLogo = async () => {
    if (!project) return;

    const requiredFields: (keyof LogoBriefData)[] = ['brandName', 'industry', 'targetAudience'];
    const missingFields = requiredFields.filter(field => !briefData[field] || String(briefData[field]).trim() === '');
    
    if (missingFields.length > 0) {
        const missingFieldNames = missingFields.map(f => {
            const fieldConfig = LOGO_BRIEF_FORM_CONFIG.find(conf => conf.name === f);
            return fieldConfig ? t(fieldConfig.labelKey, { defaultValue: f }) : f;
        }).join(', ');
        showNotification({ type: 'warning', title: t('notifications.inputRequiredTitle'), message: `${t('logoGeneration.errorBriefRequired')} (${missingFieldNames})` });
        return;
    }

    setIsGeneratingLogo(true);
    try {
      saveLogoBriefToDataCore(project.id, briefData); // Save the current brief state to dataCore
      await generateAndSetProjectLogo(project.id, briefData); // This will update strategicOutput.generatedLogo
      // No need to call onClose here, it allows user to see the generated logo in the modal
    } catch (error: any) {
      // Error notification is handled by generateAndSetProjectLogo in useProjects
      console.error("Error in handleGenerateLogo (LogoGenerationModal):", error);
    } finally {
      setIsGeneratingLogo(false);
    }
  };

  const renderFormField = (fieldConfig: LogoFormFieldDefinition) => {
    const { name, labelKey, type, tooltipKey, placeholderKey, options, isColorInput } = fieldConfig;
    const fieldId = `logoBrief_${name}`;
    const commonInputClasses = "mt-1 block w-full px-3 py-1.5 border border-neutral-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm text-xs bg-base-100";

    const label = t(labelKey);
    const placeholder = placeholderKey ? t(placeholderKey) : undefined;

    if (type === 'checkbox') {
      return (
        <div key={name} className="flex items-center mt-2 mb-1">
          <input
            id={fieldId}
            name={name}
            type="checkbox"
            checked={!!briefData[name]} // Ensure boolean value
            onChange={handleInputChange}
            className="h-4 w-4 text-primary border-neutral-300 rounded focus:ring-primary"
          />
          <label htmlFor={fieldId} className="ml-2 block text-xs text-neutral-700">{label}</label>
        </div>
      );
    }

    return (
      <div key={name} className="mb-3">
        <label htmlFor={fieldId} className="block text-xs font-medium text-neutral-700">{label}</label>
        {type === 'text' && (
          <input
            type={isColorInput ? "color" : "text"} // Use "color" type for color picker
            id={fieldId}
            name={name}
            value={String(briefData[name] || '')}
            onChange={handleInputChange}
            className={`${commonInputClasses} ${isColorInput ? 'h-10 p-1' : ''}`}
            placeholder={placeholder}
          />
        )}
        {type === 'textarea' && (
          <textarea
            id={fieldId}
            name={name}
            value={String(briefData[name] || '')}
            onChange={handleInputChange}
            rows={2}
            className={commonInputClasses}
            placeholder={placeholder}
          />
        )}
        {type === 'select' && options && (
          <select
            id={fieldId}
            name={name}
            value={String(briefData[name] || '')}
            onChange={handleInputChange}
            className={`${commonInputClasses} bg-base-100`}
          >
            {options.map(opt => (
              <option key={opt.value} value={opt.value}>{t(opt.labelKey)}</option>
            ))}
          </select>
        )}
        {name === 'style' && briefData.style === 'other' && (
            <input type="text" name="styleOther" value={briefData.styleOther || ''} onChange={handleInputChange} className={`${commonInputClasses} mt-1.5`} placeholder={t('logoGeneration.form.styleOther.placeholder')} />
        )}
        {name === 'mood' && briefData.mood === 'other_mood' && (
            <input type="text" name="moodOther" value={briefData.moodOther || ''} onChange={handleInputChange} className={`${commonInputClasses} mt-1.5`} placeholder={t('logoGeneration.form.moodOther.placeholder')} />
        )}
      </div>
    );
  };


  if (!isOpen) return null;
  if (!project) {
    return (
      <div className="fixed inset-0 bg-neutral-900/70 backdrop-blur-sm flex items-center justify-center z-[1000] p-4" onClick={onClose}>
        <div className="bg-base-100 p-6 rounded-xl shadow-2xl w-full max-w-md text-center" onClick={e => e.stopPropagation()}>
          <p>{t('projectPage.projectNotFound')}</p>
          <button onClick={onClose} className="btn-secondary mt-4">{t('common.close')}</button>
        </div>
      </div>
    );
  }

  const currentLogoUrl = project.strategicOutput?.generatedLogo?.url;

  return (
    <div
        className="fixed inset-0 bg-neutral-900/70 backdrop-blur-sm flex items-center justify-center z-[1000] p-4 transition-opacity duration-300 ease-in-out"
        onClick={onClose}
        role="dialog"
        aria-modal="true"
        aria-labelledby="logo-generation-modal-title"
    >
      <div
        className="bg-base-100 rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] flex flex-col"
        onClick={e => e.stopPropagation()}
      >
        <div className="flex justify-between items-center p-4 sm:p-5 border-b border-neutral-200">
          <h3 id="logo-generation-modal-title" className="text-lg font-semibold text-neutral-800 flex items-center">
            {ICONS.PAINT_BRUSH && React.cloneElement(ICONS.PAINT_BRUSH, {className:"w-5 h-5 mr-2 text-primary"})}
            {t('logoGeneration.modalTitle')} for "{project.name}"
          </h3>
          <button onClick={onClose} className="p-1 text-neutral-500 hover:text-error rounded-full focus-visible:ring-2 focus-visible:ring-error" aria-label={t('common.close')}>
            {ICONS.X_MARK && React.cloneElement(ICONS.X_MARK, {className: "w-5 h-5"})}
          </button>
        </div>

        <div className="p-4 sm:p-5 flex-grow overflow-y-auto grid grid-cols-1 md:grid-cols-2 gap-6 custom-scrollbar">
          <div className="space-y-3">
            <div className="flex justify-between items-center mb-2">
                 <h4 className="text-sm font-semibold text-neutral-700">{t('projectPage.assetGenerator.modal.aiDevSpec.formTitle', {defaultValue: "Logo Brief"})}</h4>
                 <button
                    onClick={handleAIAssistBrief}
                    disabled={isLoadingBrief || isGeneratingLogo}
                    className="btn-secondary text-xs px-2 py-1 flex items-center disabled:opacity-50"
                 >
                    {isLoadingBrief ? <LoadingSpinner size="xs"/> : ICONS.AI_SPARKLE && React.cloneElement(ICONS.AI_SPARKLE, {className:"w-3 h-3"})}
                    <span className="ml-1">{t('logoGeneration.aiAssistedBriefButton')}</span>
                 </button>
            </div>
            <div className="p-3 bg-secondary/30 rounded-md border border-neutral-200/70 max-h-[calc(80vh-200px)] md:max-h-full overflow-y-auto custom-scrollbar">
                {LOGO_BRIEF_FORM_CONFIG.map(renderFormField)}
            </div>
          </div>

          <div className="space-y-4 flex flex-col">
             <button
                onClick={handleGenerateLogo}
                disabled={isGeneratingLogo || isLoadingBrief || isProjectsHookLoading}
                className="btn-primary w-full py-2.5 text-sm flex items-center justify-center disabled:opacity-60"
              >
                {isGeneratingLogo ? (
                  <LoadingSpinner
                    variant="dots"
                    size="xs"
                    color="text-base-100"
                    icon={React.cloneElement(ICONS.AI_SPARKLE, {className:"w-3.5 h-3.5"})}
                    containerClassName="flex flex-row items-center"
                  />
                ) : (
                  ICONS.AI_SPARKLE && React.cloneElement(ICONS.AI_SPARKLE, {className:"w-4 h-4"})
                )}
                <span className="ml-2">{t('logoGeneration.generateButton')}</span>
              </button>

            {(isGeneratingLogo && !currentLogoUrl) && (
                <div className="flex-grow flex items-center justify-center bg-neutral-100 rounded-md p-4">
                    <LoadingSpinner message={t('logoGeneration.generatingMessage')} />
                </div>
            )}

            {currentLogoUrl && (
              <div className="mt-3 p-3 border border-neutral-200 rounded-md bg-neutral-50 text-center flex-grow flex flex-col items-center justify-center">
                <h5 className="text-xs font-semibold text-neutral-600 mb-2">{t('projectPage.nav.projectLogo')}</h5>
                <img
                    src={currentLogoUrl}
                    alt={t('projectPage.imageGeneration.altLogo', {projectName: project.name})}
                    className="max-w-full max-h-48 object-contain rounded-md shadow-sm mb-3"
                />
              </div>
            )}

            {!currentLogoUrl && !isGeneratingLogo && (
                <div className="flex-grow flex items-center justify-center bg-neutral-100 rounded-md p-4 text-center text-sm text-neutral-500 min-h-[150px]">
                    {t('projectPage.imageGeneration.noImageYet', {asset: t('projectPage.nav.projectLogo')})}
                </div>
            )}
          </div>
        </div>

        <div className="p-4 sm:p-5 border-t border-neutral-200 flex justify-end space-x-3">
          <button onClick={onClose} className="btn-secondary px-4 py-2 text-sm">
            {t('common.close')}
          </button>
        </div>
      </div>
    </div>
  );
};
