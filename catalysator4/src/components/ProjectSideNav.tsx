
import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { ICONS } from '../config'; // Updated import

interface NavSection {
  id: string;
  labelKey: string;
  icon?: JSX.Element;
  hasNewInsight?: boolean; // New prop for indicator dot
}

interface ProjectSideNavProps {
  sections: NavSection[];
  onNavClick: (sectionId: string) => void;
  activeSectionId: string | null;
}

const ProjectSideNav: React.FC<ProjectSideNavProps> = ({ sections, onNavClick, activeSectionId }) => {
  const { t } = useTranslation();
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false);
  const navRef = useRef<HTMLElement>(null);

  const handleNavClick = (sectionId: string) => {
    onNavClick(sectionId);
    setIsMobileNavOpen(false);
  };
  
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (navRef.current && !navRef.current.contains(event.target as Node)) {
        setIsMobileNavOpen(false);
      }
    };
    if (isMobileNavOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMobileNavOpen]);


  const navItems = (
    <ul className="space-y-1">
      {sections.map((section) => (
        <li key={section.id}>
          <button
            onClick={() => handleNavClick(section.id)}
            className={`w-full flex items-center px-3 py-2.5 text-sm font-medium rounded-md transition-colors duration-150 relative group
                        ${activeSectionId === section.id 
                            ? 'bg-primary text-base-100 shadow-md' 
                            : 'text-neutral-70 hover:bg-secondary hover:text-primary'
                        }`}
            aria-current={activeSectionId === section.id ? 'page' : undefined}
          >
            {section.icon && React.cloneElement(section.icon, { className: `w-5 h-5 mr-3 flex-shrink-0 ${activeSectionId === section.id ? 'text-base-100' : 'text-neutral-500 group-hover:text-primary'}` })}
            <span className="truncate flex-1 text-left">{t(section.labelKey, section.labelKey.split('.').pop())}</span>
            {section.hasNewInsight && (
                <span className="w-2 h-2 bg-accent rounded-full ml-auto animate-pulse" title={t('projectPage.newAiInsightIndicatorTitle')}></span>
            )}
          </button>
        </li>
      ))}
    </ul>
  );

  return (
    <>
      <div className="md:hidden fixed bottom-4 right-4 z-[60]">
        <button
          onClick={() => setIsMobileNavOpen(!isMobileNavOpen)}
          className="p-3 bg-primary text-base-100 rounded-full shadow-lg focus:outline-none focus:ring-2 focus:ring-accent"
          aria-expanded={isMobileNavOpen}
          aria-controls="mobile-side-nav"
        >
          {isMobileNavOpen 
            ? React.cloneElement(ICONS.X_MARK, {className: "w-6 h-6"}) 
            : React.cloneElement(ICONS.MENU, {className: "w-6 h-6"})}
        </button>
      </div>
      
      {isMobileNavOpen && (
         <div 
            id="mobile-side-nav"
            ref={navRef as React.RefObject<HTMLDivElement>}
            className="md:hidden fixed inset-x-0 bottom-0 z-50 p-4 bg-base-100 border-t border-neutral-30/50 shadow-2xl rounded-t-lg
                       transform transition-transform duration-300 ease-in-out"
            style={{ transform: isMobileNavOpen ? 'translateY(0)' : 'translateY(100%)' }}
          >
          <nav className="max-h-[50vh] overflow-y-auto">
            {navItems}
          </nav>
        </div>
      )}

      <aside ref={navRef} className="hidden md:block md:w-64 lg:w-72 sticky top-20 self-start max-h-[calc(100vh-5rem-env(safe-area-inset-bottom))] overflow-y-auto p-4 bg-base-100 rounded-lg shadow-sm border border-neutral-30/30">
        <h3 className="text-xs font-semibold text-neutral-500 uppercase tracking-wider mb-2 px-3">{t('projectPage.sideNavLabel')}</h3>
        <nav aria-label={t('projectPage.sideNavLabel')}>
          {navItems}
        </nav>
      </aside>
    </>
  );
};

export default ProjectSideNav;
