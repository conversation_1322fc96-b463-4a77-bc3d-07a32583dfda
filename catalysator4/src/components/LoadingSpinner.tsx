import React from 'react';
import { useTranslation } from 'react-i18next';

interface LoadingSpinnerProps {
  message?: string;
  size?: 'xs' | 'sm' | 'md' | 'lg'; // Added 'xs' for smaller contexts like buttons
  color?: string; // Tailwind text color class e.g. text-primary
  variant?: 'default' | 'dots' | 'line-pulse' | 'icon-pulse';
  icon?: JSX.Element;
  iconClassName?: string;
  messageClassName?: string;
  containerClassName?: string; // For custom layout of container
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  message,
  size = 'md',
  color = 'text-primary',
  variant = 'default',
  icon,
  iconClassName = 'w-6 h-6',
  messageClassName = 'text-sm text-neutral-700',
  containerClassName = 'flex flex-col items-center justify-center my-4'
}) => {
  const { t } = useTranslation();

  const sizeClasses = {
    xs: 'w-4 h-4 border-2',
    sm: 'w-5 h-5 border-2',
    md: 'w-8 h-8 border-4',
    lg: 'w-12 h-12 border-4',
  };

  const dotSizeClasses = {
    xs: 'w-1.5 h-1.5',
    sm: 'w-2 h-2',
    md: 'w-2.5 h-2.5',
    lg: 'w-3 h-3',
  }

  const renderSpinner = () => {
    switch (variant) {
      case 'dots':
        return (
          <div className="flex space-x-1.5 items-center">
            {icon && React.cloneElement(icon, { className: `${iconClassName} ${color} mr-2`})}
            <div className={`animate-ai-thinking-dots animate-ai-thinking-dots-1 ${dotSizeClasses[size]} ${color} rounded-full`}></div>
            <div className={`animate-ai-thinking-dots animate-ai-thinking-dots-2 ${dotSizeClasses[size]} ${color} rounded-full`}></div>
            <div className={`animate-ai-thinking-dots animate-ai-thinking-dots-3 ${dotSizeClasses[size]} ${color} rounded-full`}></div>
          </div>
        );
      case 'icon-pulse':
        if (icon) {
          return React.cloneElement(icon, { className: `${iconClassName} ${color} animate-soft-pulse` });
        }
        // Fallback to default if icon-pulse is chosen but no icon provided
        return <div className={`animate-spin rounded-full ${sizeClasses[size]} ${color} border-solid border-t-transparent`}></div>;
      case 'line-pulse':
        return (
            <div className="w-full h-1.5 bg-neutral-200 rounded-full overflow-hidden">
                <div className={`h-full ${color} rounded-full animate-line-pulse-h`}></div>
            </div>
        );
      case 'default':
      default:
        return (
          <div className="flex items-center">
            {icon && React.cloneElement(icon, { className: `${iconClassName} ${color} mr-2`})}
            <div className={`animate-spin rounded-full ${sizeClasses[size]} ${color} border-solid border-t-transparent`}></div>
          </div>
        );
    }
  };

  return (
    <div className={containerClassName} aria-busy="true">
      {renderSpinner()}
      {message && <p className={`mt-2 ${messageClassName}`}>{message}</p>}
    </div>
  );
};

export default LoadingSpinner;