
import React from 'react';
import { useTranslation } from 'react-i18next';
import { AISuggestion } from '../types';
import { ICONS } from '../config'; // Updated import

interface AISuggestionCardProps {
  suggestion: AISuggestion;
}

const AISuggestionCard: React.FC<AISuggestionCardProps> = ({ suggestion }) => {
  const { t } = useTranslation();

  const getAppearance = () => {
    let icon = ICONS.AI_SPARKLE;
    let titleColor = 'text-primary'; // Default if no specific color determined
    let borderColor = 'border-primary/30';
    let bgColor = 'bg-primary/5';
    let priorityBadgeClasses = 'bg-neutral-200 text-neutral-700'; // Default for low or undefined priority

    const priorityKey = suggestion.priority ? `priority.${suggestion.priority}` : 'priority.low';
    const priorityText = suggestion.priority ? t(priorityKey, {defaultValue: suggestion.priority.charAt(0).toUpperCase() + suggestion.priority.slice(1) }) : "";


    switch (suggestion.type) {
      case 'opportunity':
        icon = ICONS.LIGHTBULB;
        titleColor = 'text-ai-opportunity-high'; 
        borderColor = 'border-ai-opportunity-high/50';
        bgColor = 'bg-ai-opportunity-high/10';
        if (suggestion.priority === 'medium') {
            titleColor = 'text-ai-opportunity-medium';
            borderColor = 'border-ai-opportunity-medium/50';
            bgColor = 'bg-ai-opportunity-medium/10';
        }
        break;
      case 'risk':
        icon = ICONS.WARNING; // Changed from INFO for risks
        titleColor = 'text-ai-risk-high';
        borderColor = 'border-ai-risk-high/50';
        bgColor = 'bg-ai-risk-high/10';
         if (suggestion.priority === 'medium') {
            titleColor = 'text-ai-risk-medium';
            borderColor = 'border-ai-risk-medium/50';
            bgColor = 'bg-ai-risk-medium/10';
        }
        break;
      case 'next_step':
        icon = ICONS.CHECK_CIRCLE; 
        titleColor = 'text-ai-next-step';
        borderColor = 'border-ai-next-step/50';
        bgColor = 'bg-ai-next-step/10';
        break;
      case 'synergy':
        icon = ICONS.LINK; // Changed from GRAPH
        titleColor = 'text-ai-synergy';
        borderColor = 'border-ai-synergy/50';
        bgColor = 'bg-ai-synergy/10';
        break;
    }

    if (suggestion.priority === 'high') {
        priorityBadgeClasses = 'bg-error text-base-100'; 
    } else if (suggestion.priority === 'medium') {
        priorityBadgeClasses = 'bg-warning text-neutral-800';
    }


    return { icon, titleColor, borderColor, bgColor, priorityBadgeClasses, priorityText };
  };

  const { icon, titleColor, borderColor, bgColor, priorityBadgeClasses, priorityText } = getAppearance();

  return (
    <div className={`p-4 rounded-lg border-l-4 ${borderColor} ${bgColor} shadow-sm hover:shadow-md transition-shadow`}>
      <div className="flex items-start space-x-3">
        <span className={`flex-shrink-0 w-6 h-6 ${titleColor} mt-0.5`}>
          {icon && React.cloneElement(icon, { className: "w-full h-full"})}
        </span>
        <div className="flex-1">
          <div className="flex justify-between items-start mb-1">
            <h4 className={`text-md font-semibold ${titleColor}`}>{suggestion.title}</h4>
            {suggestion.priority && (
              <span className={`ml-2 px-2.5 py-0.5 text-xs font-bold rounded-full ${priorityBadgeClasses} flex-shrink-0`}>
                {priorityText}
              </span>
            )}
          </div>
          <p className="text-sm text-neutral-700">{suggestion.description}</p>
        </div>
      </div>
    </div>
  );
};

export default AISuggestionCard;
