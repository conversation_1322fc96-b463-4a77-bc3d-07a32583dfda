
import { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Project, AIAssistantNote, AIAssistantPrompt } from '../../../types';
import { useProjects } from '../../../hooks/useProjects';
import { useNotifications } from '../../../contexts/NotificationContext';

export const useAIAssistantNotes = (project: Project | null) => {
  const { t, i18n } = useTranslation();
  const { addAssistantNoteToDataCore, addConceptualFileToProject } = useProjects();
  const { showNotification } = useNotifications();

  const [noteTitle, setNoteTitle] = useState<string>('');
  const [showSaveNoteFields, setShowSaveNoteFields] = useState<boolean>(false);

  const getNoteTitlePlaceholder = useCallback((promptTitle: string) => {
    return t('aiAssistant.saveNoteTitlePlaceholder', { promptTitle });
  }, [t]);

  const handleSaveNote = useCallback((aiResponse: string | null, selectedPrompt: AIAssistantPrompt | null, contextInput: string) => {
    if (!project || !aiResponse || !selectedPrompt) return;
    const finalNoteTitle = noteTitle.trim() || getNoteTitlePlaceholder(selectedPrompt.title);
    addAssistantNoteToDataCore(project.id, finalNoteTitle, aiResponse, selectedPrompt.title, contextInput);
    showNotification({ type: 'success', title: t('notifications.noteSavedSuccessTitle'), message: t('aiAssistant.noteSaved') });
    setShowSaveNoteFields(false);
  }, [project, noteTitle, addAssistantNoteToDataCore, showNotification, t, getNoteTitlePlaceholder]);

  const handleCreateMarkdownFileFromNote = useCallback((note: AIAssistantNote) => {
    if (!project) return;

    const sanitizedTitle = note.title.replace(/[^\w\s-]/gi, '').replace(/\s+/g, '_');
    const fileName = `AI_Assistant_Note_-_${sanitizedTitle || 'Untitled'}.md`;

    const markdownContent = `
# ${note.title}

_Generated on: ${new Date(note.createdAt).toLocaleString(i18n.language)}_

## Prompt Used
\`\`\`
${note.promptUsed}
\`\`\`

${note.contextProvided ? `## Context Provided
\`\`\`text
${note.contextProvided}
\`\`\`
` : '## Context Provided\n_No additional context was provided._'}

---

## AI Output
${note.content}
    `;

    addConceptualFileToProject(project.id, fileName, 'text/markdown', markdownContent.trim().length, markdownContent.trim());
    showNotification({ type: 'success', title: t('notifications.fileCreatedSuccessTitle'), message: t('aiAssistant.notes.markdownFileCreated', { fileName }) });
  }, [project, addConceptualFileToProject, showNotification, i18n.language]);

  const openSaveNoteFields = useCallback((currentSelectedPrompt: AIAssistantPrompt | null) => {
    setShowSaveNoteFields(true);
    if (currentSelectedPrompt) {
        setNoteTitle(getNoteTitlePlaceholder(currentSelectedPrompt.title));
    }
  }, [getNoteTitlePlaceholder]);

  const closeSaveNoteFields = useCallback(() => {
    setShowSaveNoteFields(false);
  }, []);


  return {
    noteTitle,
    setNoteTitle,
    showSaveNoteFields,
    setShowSaveNoteFields,
    handleSaveNote,
    handleCreateMarkdownFileFromNote,
    getNoteTitlePlaceholder,
    openSaveNoteFields,
    closeSaveNoteFields
  };
};
