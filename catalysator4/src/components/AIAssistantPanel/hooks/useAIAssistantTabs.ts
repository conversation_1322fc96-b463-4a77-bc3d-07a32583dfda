
import { useState, useCallback } from 'react';

export type AIAssistantTab = 'basic' | 'super' | 'notes';

export const useAIAssistantTabs = (initialTab: AIAssistantTab = 'basic') => {
  const [activeTab, setActiveTab] = useState<AIAssistantTab>(initialTab);

  const handleTabChange = useCallback((tab: AIAssistantTab, onTabChange?: () => void) => {
    setActiveTab(tab);
    if (onTabChange) {
      onTabChange();
    }
  }, []);

  return {
    activeTab,
    handleTabChange,
  };
};
