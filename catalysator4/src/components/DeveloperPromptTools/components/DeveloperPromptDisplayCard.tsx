
import React from 'react';
import { useTranslation } from 'react-i18next';
import { ICONS } from '../../../config'; // Adjusted path
import LoadingSpinner from '../../LoadingSpinner'; // Adjusted path

interface DeveloperPromptDisplayCardProps {
  generatedPrompt: string;
  isLoading: boolean;
  error: string | null;
  copySuccess: string;
  onCopyPrompt: () => void;
  onCreateProject: () => Promise<void>;
  isCreatingProject: boolean;
  titleKey: string; // e.g., 'newAppIdea.generatedPromptTitle'
  instructionKey: string; // e.g., 'newAppIdea.promptInstruction'
  createButtonKey: string; // e.g., 'newAppIdea.createProjectButton'
  createButtonIcon?: JSX.Element;
  errorContextKey: string; // e.g., 'newAppIdea'
}

export const DeveloperPromptDisplayCard: React.FC<DeveloperPromptDisplayCardProps> = ({
  generatedPrompt,
  isLoading,
  error,
  copySuccess,
  onCopyPrompt,
  onCreateProject,
  isCreatingProject,
  titleKey,
  instructionKey,
  createButtonKey,
  createButtonIcon = ICONS.PROJECT,
  errorContextKey,
}) => {
  const { t, i18n } = useTranslation();

  if (isLoading) {
    return <LoadingSpinner variant="dots" icon={React.cloneElement(ICONS.AI_SPARKLE, {className:"w-6 h-6 text-accent"})} message={t(`${errorContextKey}.loadingMessage`)} containerClassName="flex flex-row items-center justify-center space-x-3 my-8 p-4" messageClassName="text-lg text-neutral-700" />;
  }

  if (error && !generatedPrompt.startsWith(t('serviceMessages.apiKeyMissingErrorSummary', {lng: i18n.language}))) { // Only show specific error div if it's not the API key prompt itself
    return (
      <div className="mt-4 p-4 text-sm text-status-error bg-status-error/10 rounded-lg" role="alert">
        <span className="font-medium">{t(`${errorContextKey}.errorTitle`)}:</span> {error}
      </div>
    );
  }
  
  if (!generatedPrompt) {
    return null; // Or a placeholder if preferred when there's no error but no prompt yet
  }

  const isApiErrorDisplayedInPrompt = generatedPrompt.startsWith(t('serviceMessages.apiKeyMissingErrorSummary', {lng: i18n.language}));


  return (
    <div className="mt-6 space-y-6">
      <div className="p-4 border border-neutral-200 rounded-md bg-ai-developer-prompt/5 border-l-4 border-ai-developer-prompt shadow-sm">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-xl font-semibold text-neutral-800">{t(titleKey)}</h2>
          {!isApiErrorDisplayedInPrompt && (
            <button
              onClick={onCopyPrompt}
              className="text-xs text-primary hover:underline flex items-center disabled:opacity-50 px-2 py-1 rounded hover:bg-primary/10"
              disabled={!!copySuccess || isApiErrorDisplayedInPrompt}
              title={t(`${errorContextKey}.copyPrompt`)}
            >
              {ICONS.CLIPBOARD && React.cloneElement(ICONS.CLIPBOARD, { className: "w-4 h-4 mr-1" })}
              {copySuccess || t(`${errorContextKey}.copyPrompt`)}
            </button>
          )}
        </div>
        {!isApiErrorDisplayedInPrompt && <p className="text-xs text-neutral-600 mb-3">{t(instructionKey)}</p>}
        <pre className="whitespace-pre-wrap break-words bg-base-100 p-3 rounded-md shadow-inner text-sm leading-relaxed text-neutral-800 border border-neutral-200 max-h-[50vh] overflow-y-auto">
          {generatedPrompt}
        </pre>
      </div>

      {!isApiErrorDisplayedInPrompt && (
        <div className="text-center">
          <button
            onClick={onCreateProject}
            disabled={isCreatingProject || isLoading || !!error || isApiErrorDisplayedInPrompt}
            className="btn-accent inline-flex items-center px-6 py-3 text-base font-medium rounded-md shadow-lg hover:shadow-xl transition-shadow disabled:opacity-60"
          >
            {isCreatingProject ? (
              <LoadingSpinner size="xs" color="text-base-100" containerClassName='flex items-center' />
            ) : (
              createButtonIcon && React.cloneElement(createButtonIcon, { className: "w-5 h-5 mr-2" })
            )}
            <span className="ml-2">{t(createButtonKey)}</span>
          </button>
        </div>
      )}
       {isApiErrorDisplayedInPrompt && (
          <p className="text-status-error text-xs mt-2 text-center">{t(`${errorContextKey}.apiKeyErrorNote`)}</p>
      )}
    </div>
  );
};
