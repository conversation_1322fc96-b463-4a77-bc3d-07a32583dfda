
import { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNotifications } from '../../../contexts/NotificationContext'; // Adjusted path
import { geminiService } from '../../../services/geminiService'; // Adjusted path

interface UseDeveloperPromptGeneratorArgs {
  metaPromptTemplate: string;
  generationServiceCall: (
    userInput: string, // For ProAppPage, could be general context for NewAppIdeaPage
    metaPrompt: string,
    lang: string
  ) => Promise<string>;
  errorContextKey: string; // e.g., 'newAppIdea' or 'proAppka' for specific error messages
}

export const useDeveloperPromptGenerator = ({
  metaPromptTemplate,
  generationServiceCall,
  errorContextKey
}: UseDeveloperPromptGeneratorArgs) => {
  const { t, i18n } = useTranslation();
  const { showNotification } = useNotifications();

  const [generatedPrompt, setGeneratedPrompt] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [copySuccess, setCopySuccess] = useState<string>('');

  const generatePrompt = useCallback(async (userInput: string = "") => {
    setIsLoading(true);
    setError(null);
    setGeneratedPrompt('');
    setCopySuccess('');
    try {
      const prompt = await generationServiceCall(userInput, metaPromptTemplate, i18n.language);
      
      const apiKeyErrorString = t('serviceMessages.apiKeyMissingError', { lng: i18n.language });
      const apiQuotaExceededString = t('serviceMessages.apiQuotaExceededErrorTitle', {lng: i18n.language});
      
      if (prompt.startsWith(apiKeyErrorString.substring(0, 10)) || prompt.startsWith(apiQuotaExceededString.substring(0,10))) {
        const errorMessage = prompt.includes(apiKeyErrorString) 
          ? t('serviceMessages.apiKeyMissingErrorSummary', {lng: i18n.language})
          : prompt; // Use the detailed quota message from service
        setError(errorMessage);
        setGeneratedPrompt(errorMessage);
        showNotification({ type: prompt.startsWith(apiQuotaExceededString.substring(0,10)) ? 'api_quota_exceeded' : 'error', title: t('notifications.apiErrorTitle'), message: errorMessage });
      } else {
        setGeneratedPrompt(prompt);
      }
    } catch (err) {
      console.error(`Failed to generate ${errorContextKey} developer prompt:`, err);
      const errorMessage = err instanceof Error ? err.message : t(`${errorContextKey}.generationErrorGeneral`);
      setError(errorMessage);
      setGeneratedPrompt(t(`${errorContextKey}.generationErrorInstruction`, { error: errorMessage }));
      showNotification({ type: 'error', title: t(`${errorContextKey}.errorTitle`), message: errorMessage });
    } finally {
      setIsLoading(false);
    }
  }, [generationServiceCall, metaPromptTemplate, i18n.language, t, showNotification, errorContextKey]);

  const handleCopyPrompt = useCallback(() => {
    if (!generatedPrompt || error) return;
    navigator.clipboard.writeText(generatedPrompt).then(() => {
      setCopySuccess(t(`${errorContextKey}.promptCopied`));
      setTimeout(() => setCopySuccess(''), 2000);
    }).catch(err => {
      console.error('Failed to copy prompt: ', err);
      const copyFailedMsg = t('common.copyFailed');
      setCopySuccess(copyFailedMsg);
      showNotification({ type: 'error', title: t('notifications.copyErrorTitle'), message: copyFailedMsg });
      setTimeout(() => setCopySuccess(''), 2000);
    });
  }, [generatedPrompt, error, t, showNotification, errorContextKey]);

  return {
    generatedPrompt,
    isLoading,
    error,
    copySuccess,
    generatePrompt,
    handleCopyPrompt,
  };
};
