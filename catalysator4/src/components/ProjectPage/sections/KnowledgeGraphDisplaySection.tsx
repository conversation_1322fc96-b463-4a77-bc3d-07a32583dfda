
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Project } from '../../../types';
import { ICONS } from '../../../config';
import KnowledgeGraphVisualizer from '../../KnowledgeGraphVisualizer';
import { ProjectPageSectionProps } from '../ProjectPage.types'; // sectionRef removed from this type

const KnowledgeGraphDisplaySection: React.FC<ProjectPageSectionProps> = ({ project }) => { // No longer Omit
  const { t } = useTranslation();

  if (!project.strategicOutput?.knowledgeGraph) return null;

  return (
    <section id="knowledge-graph" className="bg-base-100 p-5 sm:p-6 rounded-lg shadow-xl"> {/* Removed pt-20 -mt-20 */}
      <h3 className="text-xl font-semibold text-neutral mb-3 flex items-center">
        {ICONS.GRAPH && React.cloneElement(ICONS.GRAPH, { className: "w-5 h-5 mr-2 text-ai-technical" })} {t('projectPage.knowledgeGraphTitle')}
      </h3>
      <div className="p-3 bg-ai-technical/5 border border-ai-technical/20 rounded-md shadow-inner">
        <KnowledgeGraphVisualizer graph={project.strategicOutput.knowledgeGraph} />
      </div>
    </section>
  );
};

export default KnowledgeGraphDisplaySection;
