
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Project, AISuggestion } from '../../../types';
import { ICONS } from '../../../config';
import AISuggestionCard from '../../AISuggestionCard';
import { ProjectPageSectionProps } from '../ProjectPage.types'; // sectionRef removed from this type

const StrategicSuggestionsSection: React.FC<ProjectPageSectionProps> = ({ project }) => { // No longer Omit
  const { t } = useTranslation();

  if (!project.strategicOutput?.strategicSuggestions) return null;

  return (
    <section id="strategic-suggestions" className="bg-base-100 p-5 sm:p-6 rounded-lg shadow-xl"> {/* Removed pt-20 -mt-20 */}
      <h3 className="text-xl font-semibold text-neutral mb-3 flex items-center">
        {ICONS.LIGHTBULB && React.cloneElement(ICONS.LIGHTBULB, { className: "w-5 h-5 mr-2 text-ai-next-step" })} {t('projectPage.strategicConsultantTitle')}
      </h3>
      {project.strategicOutput.strategicSuggestions.length > 0 ? (
        <div className="space-y-4">
          {project.strategicOutput.strategicSuggestions.map((suggestion: AISuggestion) => (
            <AISuggestionCard key={suggestion.id} suggestion={suggestion} />
          ))}
        </div>
      ) : (
        <p className="text-neutral-500 text-sm">{t('projectPage.noStrategicSuggestions')}</p>
      )}
    </section>
  );
};

export default StrategicSuggestionsSection;
