
import React from 'react';
import { useTranslation } from 'react-i18next';
import { Project, ProjectTemplate } from '../../../types';
import { ProjectPageSectionProps } from '../ProjectPage.types'; // sectionRef removed from this type

interface DisplayTemplateSectionProps extends ProjectPageSectionProps { // No longer Omit
  onTemplateChange: (event: React.ChangeEvent<HTMLSelectElement>) => void;
}

const DisplayTemplateSection: React.FC<DisplayTemplateSectionProps> = ({ project, onTemplateChange }) => {
  const { t } = useTranslation();

  if (!project.strategicOutput?.suggestedTemplate) return null;

  return (
    <section id="display-template" className="bg-base-100 p-5 sm:p-6 rounded-lg shadow-xl"> {/* Removed pt-20 -mt-20 */}
      <h3 className="text-xl font-semibold text-neutral mb-3">{t('projectPage.displayTemplateTitle')}</h3>
      <div className="max-w-xs">
        <label htmlFor="templateSelect" className="block text-sm font-medium text-neutral-700 mb-1">
          {t('projectPage.aiSuggested')} <span className="font-semibold text-primary">{t(`projectTemplate_${project.strategicOutput.suggestedTemplate}` as any, project.strategicOutput.suggestedTemplate)}</span>
        </label>
        <select
          id="templateSelect"
          value={project.selectedTemplate}
          onChange={onTemplateChange}
          className="block w-full pl-3 pr-10 py-2 text-base border-neutral-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md shadow-sm bg-base-100"
        >
          {Object.values(ProjectTemplate).map(template => (
            <option key={template} value={template}>{t(`projectTemplate_${template}` as any, template)}</option>
          ))}
        </select>
        <p className="text-xs text-neutral-500 mt-1">{t('projectPage.templateSelectionNote')}</p>
      </div>
    </section>
  );
};

export default DisplayTemplateSection;
