
import React from 'react';
import { useTranslation } from 'react-i18next';
import { ProjectFile } from '../../../types';
import { ICONS } from '../../../config';
import LoadingSpinner from '../../LoadingSpinner';
import MarkdownRenderer from '../../MarkdownRenderer';
import { ProjectPageSectionProps } from '../ProjectPage.types'; // sectionRef removed from this type

interface BlogPostDisplaySectionProps extends ProjectPageSectionProps { // No longer Omit
  blogPostFile: ProjectFile | undefined;
  isLoading: boolean;
}

const BlogPostDisplaySection: React.FC<BlogPostDisplaySectionProps> = ({ blogPostFile, isLoading }) => {
  const { t } = useTranslation();

  return (
    <section id="blog-post" className="bg-base-100 p-5 sm:p-6 rounded-lg shadow-xl"> {/* Removed pt-20 -mt-20 */}
      <h3 className="text-xl font-semibold text-neutral mb-3 flex items-center">
        {React.cloneElement(ICONS.PENCIL_SQUARE, { className: "w-5 h-5 mr-2 text-ai-creative" })}
        {t('projectPage.blogPostTitle')}
      </h3>
      {isLoading ? (
        <LoadingSpinner message={t('projectPage.generatingBlogPost')} />
      ) : blogPostFile?.content ? (
        <div className="prose prose-sm max-w-none p-3 bg-ai-creative/5 border border-ai-creative/20 rounded-md shadow-inner">
          <MarkdownRenderer markdown={blogPostFile.content} />
        </div>
      ) : (
        <p className="text-sm text-neutral-500">{t('projectPage.assetGenerator.contentNotYetGenerated', { assetName: t('projectPage.blogPostTitle') })}</p>
      )}
    </section>
  );
};

export default BlogPostDisplaySection;
