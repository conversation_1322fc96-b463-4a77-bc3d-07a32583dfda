
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { ProjectFile } from '../../../types';
import { ICONS } from '../../../config';

interface FileEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  file: ProjectFile | null;
  onSave: (fileId: string, newContent: string) => void;
}

const FileEditModal: React.FC<FileEditModalProps> = ({ isOpen, onClose, file, onSave }) => {
  const { t } = useTranslation();
  const [content, setContent] = useState('');

  useEffect(() => {
    if (file?.content) {
      setContent(file.content);
    } else {
      setContent('');
    }
  }, [file]);

  if (!isOpen || !file) return null;

  const handleSave = () => {
    onSave(file.id, content);
    onClose();
  };

  const isEditable = ['text/plain', 'text/markdown', 'application/json', 'text/html'].includes(file.type);

  return (
    <div
      className="fixed inset-0 bg-neutral/60 backdrop-blur-sm flex items-center justify-center z-[1050] p-4"
      onClick={onClose}
      role="dialog"
      aria-modal="true"
      aria-labelledby="file-edit-modal-title"
    >
      <div
        className="bg-base-100 p-5 sm:p-6 rounded-xl shadow-2xl w-full max-w-2xl max-h-[85vh] flex flex-col"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-between items-center mb-4">
          <h3 id="file-edit-modal-title" className="text-lg font-semibold text-neutral-800 truncate pr-4" title={file.name}>
            {t('projectPage.fileEdit.title')}: {file.name}
          </h3>
          <button onClick={onClose} className="p-1 text-neutral-500 hover:text-error rounded-full focus-visible:ring-2 focus-visible:ring-error" aria-label={t('common.close')}>
            {ICONS.X_MARK && React.cloneElement(ICONS.X_MARK, { className: "w-5 h-5" })}
          </button>
        </div>

        {isEditable ? (
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            className="flex-grow w-full p-3 border border-neutral-300 rounded-md shadow-sm focus:ring-primary focus:border-primary sm:text-sm text-xs bg-base-100 custom-scrollbar min-h-[40vh]"
            aria-label={t('projectPage.fileEdit.contentLabel', { fileName: file.name })}
          />
        ) : (
          <p className="text-neutral-500 py-10 text-center">{t('projectPage.fileEdit.notEditable', { fileType: file.type })}</p>
        )}

        <div className="mt-6 flex justify-end space-x-3">
          <button onClick={onClose} className="btn-secondary px-4 py-2 text-sm">
            {t('common.close')}
          </button>
          {isEditable && (
            <button onClick={handleSave} className="btn-primary px-4 py-2 text-sm">
              {ICONS.SAVE && React.cloneElement(ICONS.SAVE, { className: 'w-4 h-4 mr-1.5' })}
              {t('projectPage.saveChanges')}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default FileEditModal;
