import { useState, useCallback } from 'react';

export const useProjectPageModals = () => {
  const [isLogoModalOpen, setIsLogoModalOpen] = useState(false);
  const [isDevSpecModalOpen, setIsDevSpecModalOpen] = useState(false);
  const [isBlogPostModalOpen, setIsBlogPostModalOpen] = useState(false);
  const [isLandingPageModalOpen, setIsLandingPageModalOpen] = useState(false);

  const openLogoModal = useCallback(() => setIsLogoModalOpen(true), []);
  const closeLogoModal = useCallback(() => setIsLogoModalOpen(false), []);

  const openDevSpecModal = useCallback(() => setIsDevSpecModalOpen(true), []);
  const closeDevSpecModal = useCallback(() => setIsDevSpecModalOpen(false), []);

  const openBlogPostModal = useCallback(() => setIsBlogPostModalOpen(true), []);
  const closeBlogPostModal = useCallback(() => setIsBlogPostModalOpen(false), []);

  const openLandingPageModal = useCallback(() => setIsLandingPageModalOpen(true), []);
  const closeLandingPageModal = useCallback(() => setIsLandingPageModalOpen(false), []);

  return {
    isLogoModalOpen, openLogoModal, closeLogoModal,
    isDevSpecModalOpen, openDevSpecModal, closeDevSpecModal,
    isBlogPostModalOpen, openBlogPostModal, closeBlogPostModal,
    isLandingPageModalOpen, openLandingPageModal, closeLandingPageModal,
  };
};
