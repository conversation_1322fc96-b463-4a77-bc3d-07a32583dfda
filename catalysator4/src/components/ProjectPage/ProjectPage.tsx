
import React, { useEffect, useState, useMemo, useCallback } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useProjects } from '../../hooks/useProjects';
import { useNotifications } from '../../contexts/NotificationContext';
import { Project, ProjectTemplate, AIStrategicOutput } from '../../types';

import LoadingSpinner from '../LoadingSpinner';
import AIAssistantPanel from '../AIAssistantPanel';
import ProjectSideNav from '../ProjectSideNav';
import { LogoGenerationModal } from '../LogoGenerationModal';
import { AIDeveloperSpecGeneratorModal } from '../AIDeveloperSpecGeneratorModal';
import { BlogPostGeneratorModal } from '../BlogPostGeneratorModal';
import { LandingPageGeneratorModal } from '../LandingPageGeneratorModal';

// ProjectPage specific imports
import { PageSectionConfig } from './ProjectPage.types';
import {
    getMarkdownBlueprintFile,
    getAIDeveloperSpecFile,
    getBlogPostFile,
    getLandingPageDetails,
    shouldShowSectionUtil,
    generatePageSectionsConfig
} from './projectPageUtils';
import { useProjectPageModals } from './hooks/useProjectPageModals';
import { useProjectPageAssetGeneration } from './hooks/useProjectPageAssetGeneration';
import { useProjectPageImages } from './hooks/useProjectPageImages';

// Section Components
import ProjectHeaderSection from './sections/ProjectHeaderSection';
import ProjectBlueprintSection from './sections/ProjectBlueprintSection';
import AIAssetGeneratorSection from './sections/AIAssetGeneratorSection';
import AIDeveloperSpecDisplaySection from './sections/AIDeveloperSpecDisplaySection';
import BlogPostDisplaySection from './sections/BlogPostDisplaySection';
import LandingPageDisplaySection from './sections/LandingPageDisplaySection';
import AIStrategicCenterSection from './sections/AIStrategicCenterSection';
import ProjectNarrativeSection from './sections/ProjectNarrativeSection';
import GeneratedImageDisplay from './sections/GeneratedImageDisplay';
import KnowledgeGraphDisplaySection from './sections/KnowledgeGraphDisplaySection';
import StrategicSuggestionsSection from './sections/StrategicSuggestionsSection';
import DisplayTemplateSection from './sections/DisplayTemplateSection';
import ProjectFilesSection from './sections/ProjectFilesSection';


const ProjectPage: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const { t, i18n } = useTranslation();
  const { showNotification } = useNotifications();
  const {
    projects,
    getProjectById,
    runDeepAnalysis,
    isLoading: isProjectsHookLoadingGlobal,
    setSelectedTemplate: setProjectTemplateContext,
  } = useProjects();

  const project = useMemo(() => projectId ? getProjectById(projectId) : null, [projectId, getProjectById, projects]);

  const [activeSection, setActiveSection] = useState<string | null>(null);
  const [newInsights, setNewInsights] = useState<Record<string, boolean>>({});

  // Custom Hooks for managing page logic
  const {
    isLogoModalOpen, openLogoModal, closeLogoModal,
    isDevSpecModalOpen, openDevSpecModal, closeDevSpecModal,
    isBlogPostModalOpen, openBlogPostModal, closeBlogPostModal,
    isLandingPageModalOpen, openLandingPageModal, closeLandingPageModal,
  } = useProjectPageModals();

  const {
    isGeneratingAIDevSpecLocal, isGeneratingBlogPostLocal, isGeneratingLandingPageLocal,
    isZipping,
    handleDownloadAllFiles,
    handleDevSpecSubmit, handleBlogPostSubmit, handleLandingPageSubmit,
  } = useProjectPageAssetGeneration(project);

  const {
    showMainImagePrompt, isLoadingMainImage, isLoadingMainImageAlternatives,
    isLoadingLogo, isLoadingLogoAlternatives,
    toggleShowMainImagePrompt, handleGenerateImage, handleGenerateImageAlternatives, handleSelectAlternative
  } = useProjectPageImages(project);


  // Derived data using utility functions
  const markdownBlueprintFile = useMemo(() => getMarkdownBlueprintFile(project), [project]);
  const aiDeveloperSpecFile = useMemo(() => getAIDeveloperSpecFile(project, t), [project, t]);
  const blogPostFile = useMemo(() => getBlogPostFile(project, t), [project, t]);
  const landingPageDetails = useMemo(() => getLandingPageDetails(project, t), [project, t]);

  const shouldShowSection = useCallback((sectionId: string): boolean => {
    return shouldShowSectionUtil(
        sectionId, project, markdownBlueprintFile, aiDeveloperSpecFile,
        blogPostFile, landingPageDetails, isGeneratingAIDevSpecLocal,
        isGeneratingBlogPostLocal, isGeneratingLandingPageLocal
    );
  }, [project, markdownBlueprintFile, aiDeveloperSpecFile, blogPostFile, landingPageDetails, isGeneratingAIDevSpecLocal, isGeneratingBlogPostLocal, isGeneratingLandingPageLocal]);

  const pageSectionsConfig = useMemo((): PageSectionConfig[] => {
    return generatePageSectionsConfig(t, shouldShowSection);
  }, [t, shouldShowSection]);

  const availableSectionsForNav = useMemo(() => {
    const navSections = pageSectionsConfig.filter(s => s.id !== 'project-header'); 
    return navSections.map(s => ({
      ...s,
      hasNewInsight: !!(s.insightType && newInsights[s.insightType]) || (s.id === 'ai-strategic-center' && Object.values(newInsights).some(v=>v))
    }));
  }, [pageSectionsConfig, newInsights]);

  const isAnalyzing = useMemo(() =>
    project?.status === 'analyzing_deep' ||
    project?.status === 'analyzing_initial' ||
    project?.status?.startsWith('analyzing_deep_'),
    [project?.status]
  );

  // Effects
  useEffect(() => {
    if (!project && projectId) navigate('/');
  }, [project, projectId, navigate]);

  const prevStrategicOutputRef = React.useRef<AIStrategicOutput | undefined>(undefined);
  useEffect(() => {
    if (project?.strategicOutput && project.strategicOutput !== prevStrategicOutputRef.current) {
        const newAvailableInsights: Record<string, boolean> = {};
        if (project.strategicOutput.projectPageNarrative) newAvailableInsights['narrative'] = true;
        if (project.strategicOutput.generatedImage?.url) newAvailableInsights['image'] = true;
        if (project.strategicOutput.generatedLogo?.url) newAvailableInsights['logo'] = true; 
        if (project.strategicOutput.knowledgeGraph?.summary) newAvailableInsights['knowledgeGraph'] = true;
        if (project.strategicOutput.strategicSuggestions?.length > 0) newAvailableInsights['suggestions'] = true;
        if (project.strategicOutput.suggestedTemplate) newAvailableInsights['template'] = true;
        setNewInsights(prev => ({ ...prev, ...newAvailableInsights }));
        prevStrategicOutputRef.current = project.strategicOutput;
    }
  }, [project?.strategicOutput]);

  useEffect(() => {
    if (project && availableSectionsForNav.length > 0 && !activeSection) {
      let initialSecId: string | null = null;
      const strategicCenter = availableSectionsForNav.find(s => s.id === 'ai-strategic-center');
      if (strategicCenter) {
        initialSecId = strategicCenter.id;
      } else {
        const firstNavigableSection = availableSectionsForNav.find(s => s.id !== 'project-header');
        if (firstNavigableSection) {
          initialSecId = firstNavigableSection.id;
        }
      }
      if (initialSecId) {
        setActiveSection(initialSecId);
      }
    }
  }, [project, availableSectionsForNav, activeSection]); 

  // Event Handlers
  const handleNavClick = (sectionId: string) => {
    setActiveSection(sectionId); 
    const clickedSectionConfig = pageSectionsConfig.find(s => s.id === sectionId); 
    if (clickedSectionConfig?.insightType && newInsights[clickedSectionConfig.insightType]) {
        setNewInsights(prev => ({...prev, [clickedSectionConfig.insightType!]: false}));
    }
    if (sectionId === 'ai-strategic-center' && Object.values(newInsights).some(v=>v)) {
      setNewInsights({});
    }
  };

  const handleRunDeepAnalysisWrapper = async () => {
    if (project) await runDeepAnalysis(project.id);
  };

  const handleSaveChangesWrapper = () => {
    if (project) {
      showNotification({ type: 'success', title: t('notifications.changesSavedTitle'), message: t('projectPage.saveChangesAlert') });
    }
  };

  const handleTemplateChangeWrapper = (e: React.ChangeEvent<HTMLSelectElement>) => {
    if (project) {
      const newTemplate = e.target.value as ProjectTemplate;
      setProjectTemplateContext(project.id, newTemplate);
    }
  };

  // Render logic
  if (!project && isProjectsHookLoadingGlobal) return <LoadingSpinner message={t('projectPage.loadingProject')} />;
  if (!project) return (
    <div className="text-center p-8">{t('projectPage.projectNotFound')} <Link to="/" className="text-primary hover:underline">{t('projectPage.goToDashboard')}</Link></div>
  );

  const isAnyAssetGeneratorLoading = isProjectsHookLoadingGlobal || isGeneratingAIDevSpecLocal || isGeneratingBlogPostLocal || isGeneratingLandingPageLocal;

  const renderActiveSection = () => {
    if (!project || !activeSection) return null; 

    switch (activeSection) {
      case 'project-blueprint':
        return shouldShowSection('project-blueprint') && markdownBlueprintFile ? <ProjectBlueprintSection project={project} markdownBlueprintFile={markdownBlueprintFile} /> : null;
      case 'ai-asset-generator':
        return shouldShowSection('ai-asset-generator') ? (
          <AIAssetGeneratorSection
            project={project}
            onOpenLogoModal={openLogoModal}
            onOpenDevSpecModal={openDevSpecModal}
            onOpenBlogPostModal={openBlogPostModal}
            onOpenLandingPageModal={openLandingPageModal}
            isAnyAssetGeneratorLoading={isAnyAssetGeneratorLoading}
          />
        ) : null;
      case 'ai-developer-specification':
        return shouldShowSection('ai-developer-specification') && (aiDeveloperSpecFile || isGeneratingAIDevSpecLocal) ? (
            <AIDeveloperSpecDisplaySection
                project={project}
                aiDeveloperSpecFile={aiDeveloperSpecFile}
                isLoading={isGeneratingAIDevSpecLocal}
            />
        ) : null;
      case 'blog-post':
        return shouldShowSection('blog-post') && (blogPostFile || isGeneratingBlogPostLocal) ? (
            <BlogPostDisplaySection
                project={project}
                blogPostFile={blogPostFile}
                isLoading={isGeneratingBlogPostLocal}
            />
        ) : null;
      case 'landing-page':
        return shouldShowSection('landing-page') && (landingPageDetails.file || landingPageDetails.specFile || isGeneratingLandingPageLocal) ? (
            <LandingPageDisplaySection
                project={project}
                landingPageDetails={landingPageDetails}
                isLoading={isGeneratingLandingPageLocal}
            />
        ) : null;
      case 'ai-strategic-center':
        return shouldShowSection('ai-strategic-center') ? (
          <AIStrategicCenterSection
            project={project}
            onRunDeepAnalysis={handleRunDeepAnalysisWrapper}
            isAnalyzing={isAnalyzing}
            isProjectsHookLoadingGlobal={isProjectsHookLoadingGlobal}
          />
        ) : null;
      case 'ai-assistant-panel':
        return shouldShowSection('ai-assistant-panel') ? <AIAssistantPanel project={project} /> : null;
      case 'project-narrative':
        return project.strategicOutput && shouldShowSection('project-narrative') ? (
          <ProjectNarrativeSection project={project} />
        ): null;
      case 'generated-image-section':
        return project.strategicOutput && shouldShowSection('generated-image-section') ? (
          <div className="bg-base-100 p-5 sm:p-6 rounded-lg shadow-xl space-y-6">
              <GeneratedImageDisplay
                project={project}
                imageType="main"
                imageDetails={project.strategicOutput.generatedImage}
                titleKey="projectPage.generatedImageTitle"
                altKey="projectPage.imageGeneration.altMainImage"
                isLoadingThisImage={isLoadingMainImage}
                isLoadingAlternativesThisImage={isLoadingMainImageAlternatives}
                showPrompt={showMainImagePrompt}
                onTogglePrompt={toggleShowMainImagePrompt}
                onGenerate={handleGenerateImage}
                onGenerateAlternatives={handleGenerateImageAlternatives}
                onSelectAlternative={handleSelectAlternative}
              />
              <GeneratedImageDisplay
                project={project}
                imageType="logo"
                imageDetails={project.strategicOutput.generatedLogo}
                titleKey="projectPage.nav.projectLogo"
                altKey="projectPage.imageGeneration.altLogo"
                isLoadingThisImage={isLoadingLogo}
                isLoadingAlternativesThisImage={isLoadingLogoAlternatives}
                showPrompt={false} 
                onTogglePrompt={() => {}} 
                onGenerate={handleGenerateImage}
                onGenerateAlternatives={handleGenerateImageAlternatives}
                onSelectAlternative={handleSelectAlternative}
                onOpenLogoModal={openLogoModal}
              />
          </div>
        ) : null;
      case 'knowledge-graph':
        return project.strategicOutput && shouldShowSection('knowledge-graph') ? (
          <KnowledgeGraphDisplaySection project={project} />
        ) : null;
      case 'strategic-suggestions':
        return project.strategicOutput && shouldShowSection('strategic-suggestions') ? (
          <StrategicSuggestionsSection project={project} />
        ) : null;
      case 'display-template':
        return project.strategicOutput && shouldShowSection('display-template') ? (
          <DisplayTemplateSection project={project} onTemplateChange={handleTemplateChangeWrapper} />
        ) : null;
      case 'project-files':
        return shouldShowSection('project-files') ? (
          <ProjectFilesSection
            project={project}
            onDownloadAllFiles={handleDownloadAllFiles}
            isZipping={isZipping}
          />
        ) : null;
      default:
        // Attempt to render the first available section if activeSection is somehow invalid but not null
        if (availableSectionsForNav.length > 0 && !availableSectionsForNav.find(s => s.id === activeSection)) {
            const firstValidSection = availableSectionsForNav[0];
            setActiveSection(firstValidSection.id); // This will trigger a re-render
            return null; // Return null for this render pass, next pass will have the correct section
        }
        return null;
    }
  };

  return (
    <div className="flex flex-col md:flex-row gap-6">
      <ProjectSideNav
        sections={availableSectionsForNav} 
        onNavClick={handleNavClick}
        activeSectionId={activeSection}
      />
      <div className="flex-1 min-w-0"> 
        <ProjectHeaderSection
          project={project}
          onSaveChanges={handleSaveChangesWrapper}
        />
        <div className="mt-8"> 
          {renderActiveSection()} 
        </div>
      </div>

      <LogoGenerationModal isOpen={isLogoModalOpen} onClose={closeLogoModal} project={project} />
      <AIDeveloperSpecGeneratorModal
        isOpen={isDevSpecModalOpen}
        onClose={closeDevSpecModal}
        project={project}
        onSubmit={handleDevSpecSubmit}
        isLoading={isGeneratingAIDevSpecLocal || isProjectsHookLoadingGlobal && project?.status === 'generating_ai_dev_spec'}
      />
      <BlogPostGeneratorModal
        isOpen={isBlogPostModalOpen}
        onClose={closeBlogPostModal}
        project={project}
        onSubmit={handleBlogPostSubmit}
        isLoading={isGeneratingBlogPostLocal || isProjectsHookLoadingGlobal && project?.status === 'generating_blog_post'}
      />
      <LandingPageGeneratorModal
        isOpen={isLandingPageModalOpen}
        onClose={closeLandingPageModal}
        project={project}
        onSubmit={handleLandingPageSubmit}
        isLoading={isGeneratingLandingPageLocal || isProjectsHookLoadingGlobal && (project?.status === 'generating_landing_page_content' || project?.status === 'generating_landing_page_visual_spec')}
      />
    </div>
  );
};

export default ProjectPage;
