
import { Project } from '../../types';

export interface PageSectionConfig {
  id: string;
  labelKey: string;
  icon: JSX.Element;
  aiBg?: string; // Background for AI-related sections
  insightType?: string; // To link with newInsights state
}

export interface ProjectPageSectionProps {
  project: Project;
  // sectionRef prop is removed as direct DOM manipulation for scrolling is no longer the primary navigation method.
  // Each section component will manage its own root element and ID.
}
