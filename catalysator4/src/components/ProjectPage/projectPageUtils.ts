import { TFunction } from 'i18next';
import { Project, ProjectFile } from '../../types';
import { ICONS } from '../../config';
import { PageSectionConfig } from './ProjectPage.types';

export const getMarkdownBlueprintFile = (project: Project | null): ProjectFile | undefined => {
  if (!project) return undefined;
  return project.files.find(
    (f) =>
      f.type === 'text/markdown' &&
      f.content &&
      (f.name.includes('Blueprint') || f.name.includes('Developer_Prompt') || f.name.includes('Developer_Blueprint'))
  );
};

export const getAIDeveloperSpecFile = (project: Project | null, t: TFunction): ProjectFile | undefined => {
  if (!project) return undefined;
  const specFileName = t('projectPage.aiDeveloperSpecFileName', {
    projectName: project.name.replace(/[^a-z0-9]/gi, '_'),
    defaultValue: `${project.name}_AI_Developer_Spec.md`,
  });
  return project.files.find((f) => f.name.includes(specFileName));
};

export const getBlogPostFile = (project: Project | null, t: TFunction): ProjectFile | undefined => {
  if (!project) return undefined;
  const blogPostFileName = t('projectPage.blogPostFileName', {
    projectName: project.name.replace(/[^a-z0-9]/gi, '_'),
    defaultValue: `${project.name}_Blog_Post.md`,
  });
  return project.files.find((f) => f.name.includes(blogPostFileName));
};

export const getLandingPageDetails = (project: Project | null, t: TFunction): { content: string | null; file: ProjectFile | null; specFile: ProjectFile | null; } => {
  if (!project) return { content: null, file: null, specFile: null };
  const landingPageFileName = t('projectPage.landingPage.fileNameDefaultHTML', { projectName: project.name.replace(/[^a-z0-9]/gi, '_') });
  const landingPageSpecFileName = t('projectPage.landingPage.specFileNameDefaultMD', { projectName: project.name.replace(/[^a-z0-9]/gi, '_') });
  const file = project.files.find((f) => f.name === landingPageFileName);
  const specFile = project.files.find((f) => f.name === landingPageSpecFileName);
  return { content: file?.content || null, file: file || null, specFile: specFile || null };
};


export const shouldShowSectionUtil = (
    sectionId: string,
    project: Project | null,
    markdownBlueprintFile: ProjectFile | undefined,
    aiDeveloperSpecFile: ProjectFile | undefined,
    blogPostFile: ProjectFile | undefined,
    landingPageDetails: { file: ProjectFile | null; specFile: ProjectFile | null },
    isGeneratingAIDevSpecLocal: boolean,
    isGeneratingBlogPostLocal: boolean,
    isGeneratingLandingPageLocal: boolean
  ): boolean => {
    if (!project) return false;

    const isAnalyzing = project.status === 'analyzing_deep' || project.status === 'analyzing_initial' || project.status?.startsWith('analyzing_deep_');

    if (sectionId === 'project-blueprint') return !!markdownBlueprintFile?.content;
    if (sectionId === 'ai-developer-specification') return !!aiDeveloperSpecFile?.content || isGeneratingAIDevSpecLocal;
    if (sectionId === 'blog-post') return !!blogPostFile?.content || isGeneratingBlogPostLocal;
    if (sectionId === 'landing-page') return !!landingPageDetails.file || !!landingPageDetails.specFile || isGeneratingLandingPageLocal;
    
    if (['project-header', 'ai-asset-generator', 'ai-strategic-center', 'ai-assistant-panel', 'project-files'].includes(sectionId)) return true;
    
    // For sections dependent on deep analysis results
    if (project.status === 'analyzed_deep' || isAnalyzing) {
        if (sectionId === 'project-narrative' && project.strategicOutput?.projectPageNarrative) return true;
        if (sectionId === 'generated-image-section' && (project.strategicOutput?.generatedImage || project.strategicOutput?.generatedLogo)) return true;
        if (sectionId === 'knowledge-graph' && project.strategicOutput?.knowledgeGraph) return true;
        if (sectionId === 'strategic-suggestions' && project.strategicOutput?.strategicSuggestions) return true;
        if (sectionId === 'display-template' && project.strategicOutput?.suggestedTemplate) return true;
    }
    
    return false;
  };

export const generatePageSectionsConfig = (
    t: TFunction,
    shouldShowSection: (sectionId: string) => boolean
  ): PageSectionConfig[] => {
    const baseSectionsList: PageSectionConfig[] = [
      { id: 'project-header', labelKey: 'projectPage.nav.header', icon: ICONS.PROJECT },
    ];

    if (shouldShowSection('project-blueprint')) {
      baseSectionsList.push({ id: 'project-blueprint', labelKey: 'projectPage.nav.blueprint', icon: ICONS.DOCUMENT_TEXT, aiBg: 'bg-ai-developer-prompt/5' });
    }
    if (shouldShowSection('ai-asset-generator')) {
      baseSectionsList.push({ id: 'ai-asset-generator', labelKey: 'projectPage.nav.aiAssetGenerator', icon: ICONS.AI_SPARKLE, aiBg: 'bg-accent/5' });
    }
    if (shouldShowSection('ai-developer-specification')) {
      baseSectionsList.push({ id: 'ai-developer-specification', labelKey: 'projectPage.nav.aiDeveloperSpecification', icon: ICONS.CODE_BLOCK, aiBg: 'bg-ai-technical/5' });
    }
    if (shouldShowSection('blog-post')) {
      baseSectionsList.push({ id: 'blog-post', labelKey: 'projectPage.nav.blogPost', icon: ICONS.PENCIL_SQUARE, aiBg: 'bg-ai-creative/5' });
    }
    if (shouldShowSection('landing-page')) {
      baseSectionsList.push({ id: 'landing-page', labelKey: 'projectPage.nav.landingPage', icon: ICONS.LINK, aiBg: 'bg-ai-synergy/5' });
    }
    
    baseSectionsList.push(
      { id: 'ai-strategic-center', labelKey: 'projectPage.nav.strategicCenter', icon: ICONS.AI_SPARKLE },
      { id: 'ai-assistant-panel', labelKey: 'projectPage.nav.assistantPanel', icon: ICONS.WAND }
    );
    
    if (shouldShowSection('project-narrative')) {
        baseSectionsList.push({ id: 'project-narrative', labelKey: 'projectPage.nav.narrative', icon: ICONS.BOOK_OPEN, insightType: 'narrative', aiBg: 'bg-ai-creative/5' });
    }
    if (shouldShowSection('generated-image-section')) {
        baseSectionsList.push({ id: 'generated-image-section', labelKey: 'projectPage.nav.imageSection', icon: ICONS.IMAGE, insightType: 'image', aiBg: 'bg-ai-creative/5' });
    }
    if (shouldShowSection('knowledge-graph')) {
        baseSectionsList.push({ id: 'knowledge-graph', labelKey: 'projectPage.nav.knowledgeGraph', icon: ICONS.GRAPH, insightType: 'knowledgeGraph', aiBg: 'bg-ai-technical/5' });
    }
    if (shouldShowSection('strategic-suggestions')) {
        baseSectionsList.push({ id: 'strategic-suggestions', labelKey: 'projectPage.nav.suggestions', icon: ICONS.LIGHTBULB, insightType: 'suggestions', aiBg: 'bg-ai-next-step/5' });
    }
    if (shouldShowSection('display-template')) {
        baseSectionsList.push({ id: 'display-template', labelKey: 'projectPage.nav.template', icon: ICONS.PROJECT, insightType: 'template', aiBg: 'bg-ai-next-step/5' });
    }

    baseSectionsList.push({ id: 'project-files', labelKey: 'projectPage.nav.files', icon: ICONS.FOLDER });
    
    return baseSectionsList.filter(s => shouldShowSection(s.id));
  };
