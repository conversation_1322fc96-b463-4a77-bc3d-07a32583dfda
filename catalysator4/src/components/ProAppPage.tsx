
import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useProjects } from '../hooks/useProjects';
import { useNotifications } from '../contexts/NotificationContext';
import { geminiService } from '../services/geminiService';
import { META_PROMPT_FOR_UNIVERSAL_APP_CREATION, ICONS } from '../config';
import { useDeveloperPromptGenerator, DeveloperPromptDisplayCard } from './DeveloperPromptTools'; // New import

const ProAppPage: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { showNotification } = useNotifications();
  const { addProject, isLoading: isProjectsLoadingGlobal } = useProjects();

  const [userInput, setUserInput] = useState<string>('');

  const {
    generatedPrompt,
    isLoading: isLoadingPrompt,
    error,
    copySuccess,
    generatePrompt,
    handleCopyPrompt,
  } = useDeveloperPromptGenerator({
    metaPromptTemplate: META_PROMPT_FOR_UNIVERSAL_APP_CREATION,
    generationServiceCall: geminiService.generateProAppDeveloperPrompt,
    errorContextKey: 'proAppka',
  });

  const handleGeneratePromptWrapper = useCallback(async () => {
    if (!userInput.trim()) {
        showNotification({ type: 'warning', title: t('notifications.inputRequiredTitle'), message: t('proAppka.errorUserInputRequired') });
      return;
    }
    await generatePrompt(userInput);
  }, [userInput, generatePrompt, showNotification, t]);

  const handleCreateProjectFromPrompt = async () => {
    if (!generatedPrompt || generatedPrompt.startsWith(t('serviceMessages.apiKeyMissingErrorSummary', {lng: i18n.language})) || error) {
      showNotification({ type: 'error', title: t('notifications.creationFailedTitle'), message: t('proAppka.cannotCreateFromError') });
      return;
    }

    let projectName = t('proAppka.defaultProjectName');
    const namePatterns = [
        /^\*\*Project Title:\*\*\s*([^\n]+)/im, /^\*\*Názov Projektu:\*\*\s*([^\n]+)/im,
        /^Project Title:\s*([^\n]+)/im, /^Názov Projektu:\s*([^\n]+)/im,
    ];
    for (const pattern of namePatterns) {
        const match = generatedPrompt.match(pattern);
        if (match && match[1] && match[1].trim()) {
            projectName = match[1].trim();
            const uspSplit = projectName.split(/\s+-\s+/);
            if (uspSplit.length > 0) projectName = uspSplit[0].trim();
            break;
        }
    }
    
    const projectDescription = generatedPrompt;
    const newProjectId = await addProject(projectName, projectDescription, [], true);
    if (newProjectId) {
      showNotification({ type: 'success', title: t('notifications.projectCreatedSuccessTitle'), message: t('notifications.projectCreatedSuccessMessage', { projectName }) });
      navigate(`/project/${newProjectId}`);
    } else {
      showNotification({ type: 'error', title: t('notifications.creationFailedTitle'), message: t('proAppka.projectCreationError') });
    }
  };

  return (
    <div className="bg-base-100 p-6 sm:p-8 rounded-lg shadow-xl">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-3">
        <h1 className="text-2xl sm:text-3xl font-bold text-primary flex items-center">
            {ICONS.LIGHTBULB && React.cloneElement(ICONS.LIGHTBULB, { className: "w-7 h-7 sm:w-8 sm:h-8 mr-3 text-ai-next-step"})} 
            {t('proAppka.pageTitle')}
        </h1>
      </div>
      <p className="text-neutral-700 mb-6">{t('proAppka.pageDescription')}</p>

      <div className="mb-6">
        <label htmlFor="userInput" className="block text-sm font-medium text-neutral-700 mb-1">
          {t('proAppka.userInputLabel')}
        </label>
        <textarea
          id="userInput"
          value={userInput}
          onChange={(e) => setUserInput(e.target.value)}
          rows={4}
          className="w-full p-2 border border-neutral-300 rounded-md shadow-sm focus:ring-primary focus:border-primary"
          placeholder={t('proAppka.userInputPlaceholder')}
        />
      </div>

      <button
        onClick={handleGeneratePromptWrapper}
        disabled={isLoadingPrompt || isProjectsLoadingGlobal}
        className="btn-primary inline-flex items-center px-6 py-3 text-base font-medium rounded-md shadow-lg hover:shadow-xl transition-shadow disabled:opacity-60"
      >
        {React.cloneElement(ICONS.AI_SPARKLE, {className: "w-5 h-5 text-accent"})}
        <span className="ml-2">{t('proAppka.generatePromptButton')}</span>
      </button>

      <DeveloperPromptDisplayCard
        generatedPrompt={generatedPrompt}
        isLoading={isLoadingPrompt}
        error={error}
        copySuccess={copySuccess}
        onCopyPrompt={handleCopyPrompt}
        onCreateProject={handleCreateProjectFromPrompt}
        isCreatingProject={isProjectsLoadingGlobal}
        titleKey="proAppka.generatedPromptTitle"
        instructionKey="proAppka.promptInstruction"
        createButtonKey="proAppka.createProjectButton"
        errorContextKey="proAppka"
      />
    </div>
  );
};

export default ProAppPage;
