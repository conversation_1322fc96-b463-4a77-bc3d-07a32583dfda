
import React from 'react';
import { useTranslation } from 'react-i18next';
import { ICONS } from '../config'; // Updated import

const HowAIWorksPage: React.FC = () => {
  const { t } = useTranslation();

  // Helper to render list items that may contain <strong> tags
  const renderListItems = (keys: string[]) => {
    return keys.map(key => (
      <li key={key} dangerouslySetInnerHTML={{ __html: t(key) }} />
    ));
  };
  
  const generativeContentListKeys = [
    'howAiWorks.capabilities.generativeContentList.0',
    'howAiWorks.capabilities.generativeContentList.1',
    'howAiWorks.capabilities.generativeContentList.2'
  ];

  const learningMechanismsListKeys = [
    'howAiWorks.learningMechanisms.0',
    'howAiWorks.learningMechanisms.1',
    'howAiWorks.learningMechanisms.2',
    'howAiWorks.learningMechanisms.3',
    'howAiWorks.learningMechanisms.4'
  ];


  return (
    <div className="bg-base-100 p-6 sm:p-8 rounded-lg shadow-xl prose prose-indigo max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold text-primary mb-6 flex items-center">
        {ICONS.AI_SPARKLE && React.cloneElement(ICONS.AI_SPARKLE, { className: "w-7 h-7 text-accent" })} <span className="ml-3">{t('howAiWorks.pageTitle')}</span>
      </h1>

      <p className="lead">
        {t('howAiWorks.lead')}
      </p>

      <h2 className="text-2xl font-semibold text-neutral mt-8">{t('howAiWorks.coreCapabilitiesTitle')}</h2>
      <ul>
        <li dangerouslySetInnerHTML={{ __html: t('howAiWorks.capabilities.dataIngestion') }} />
        <li dangerouslySetInnerHTML={{ __html: t('howAiWorks.capabilities.semanticAnalysis') }} />
        <li>
          <span dangerouslySetInnerHTML={{ __html: t('howAiWorks.capabilities.generativeContent') }} />
          <ul>
            {renderListItems(generativeContentListKeys)}
          </ul>
        </li>
        <li dangerouslySetInnerHTML={{ __html: t('howAiWorks.capabilities.portfolioIntelligence') }} />
      </ul>

      <h2 className="text-2xl font-semibold text-neutral mt-8">{t('howAiWorks.howItLearnsTitle')}</h2>
      <p>
        {t('howAiWorks.learningDescription')}
      </p>
      <ul>
        {renderListItems(learningMechanismsListKeys)}
      </ul>
      <p>
        {t('howAiWorks.learningGoal')}
      </p>

      <div className="mt-10 p-4 bg-primary/10 border border-primary/20 rounded-lg">
        <h3 className="text-xl font-semibold text-primary flex items-center">
          {ICONS.LIGHTBULB && React.cloneElement(ICONS.LIGHTBULB, {className: "w-5 h-5"})} <span className="ml-2">{t('howAiWorks.visionTitle')}</span>
        </h3>
        <p className="mt-2 text-neutral-800">
          {t('howAiWorks.visionDescription')}
        </p>
      </div>
    </div>
  );
};

export default HowAIWorksPage;
