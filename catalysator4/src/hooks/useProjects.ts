
import React, { createContext, useState, useContext, useCallback, useEffect, ReactNode } from 'react';
import { useTranslation } from 'react-i18next'; 
import { 
    Project, 
    ProjectFile, 
    AIInitialAnalysis, 
    AIStrategicOutput, 
    ProjectTemplate, 
    PortfolioAnalysis, 
    ProjectsContextType,
    AIAssistantNote,
    GeneratedImageDetails,
    ProjectStatus,
    DeepAnalysisSubStage,
    LogoBriefData,
    AIDevSpecFormData,
    BlogPostFormData,
    LandingPageFormData
} from '../types';
import { geminiService } from '../services/geminiService';
import { v4 as uuidv4 } from 'uuid';
import i18n from '../i18n'; 
import { useNotifications } from '../contexts/NotificationContext';


const ProjectsContext = createContext<ProjectsContextType | undefined>(undefined);

export const ProjectsProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { t } = useTranslation(); 
  const { showNotification } = useNotifications();
  const [projects, setProjects] = useState<Project[]>(() => {
    const savedProjects = localStorage.getItem('aiProjects');
    try {
        const parsedProjects = savedProjects ? JSON.parse(savedProjects) : [];
        return parsedProjects.map((p: Project) => ({ 
            ...p,
            dataCore: {
                ...p.dataCore,
                assistantNotes: p.dataCore?.assistantNotes || [],
                logoBrief: p.dataCore?.logoBrief || undefined,
                aiDevSpecFormData: p.dataCore?.aiDevSpecFormData || undefined,
                blogPostFormData: p.dataCore?.blogPostFormData || undefined,
                landingPageFormData: p.dataCore?.landingPageFormData || undefined,
            },
            files: p.files || [], 
            isFavorite: p.isFavorite || false,
            lastInteractionTimestamp: p.lastInteractionTimestamp || p.updatedAt || new Date().toISOString(),
            selectedTemplate: p.selectedTemplate || ProjectTemplate.STANDARD,
            status: p.status || 'new',
            deepAnalysisSubStage: p.deepAnalysisSubStage || null,
        }));
    } catch (e) {
        console.error("Failed to parse projects from localStorage", e);
        localStorage.removeItem('aiProjects'); 
        return [];
    }
  });
  const [isLoading, setIsLoading] = useState<boolean>(false); 
  const [portfolioAnalysis, setPortfolioAnalysis] = useState<PortfolioAnalysis | null>(null);
  const [isLoadingPortfolio, setIsLoadingPortfolio] = useState<boolean>(false);

  useEffect(() => {
    localStorage.setItem('aiProjects', JSON.stringify(projects));
  }, [projects]);

  const updateProjectState = useCallback((projectId: string, updates: Partial<Project>) => {
    setProjects(prev => prev.map(p => {
        if (p.id === projectId) {
            const now = new Date().toISOString();
            const updatedProject: Project = { 
                ...p, 
                ...updates, 
                updatedAt: now,
                lastInteractionTimestamp: updates.lastInteractionTimestamp || (Object.keys(updates).length > 1 || !updates.lastInteractionTimestamp ? now : p.lastInteractionTimestamp || now)
            };
            
            updatedProject.dataCore = {
                ...(p.dataCore || { assistantNotes: [] }), 
                ...(updates.dataCore || {}),
                assistantNotes: (updates.dataCore?.assistantNotes || p.dataCore?.assistantNotes || [])
            };
            if (!updatedProject.files) { 
                updatedProject.files = [];
            }
            if (typeof updatedProject.isFavorite === 'undefined') { 
                updatedProject.isFavorite = false;
            }
            return updatedProject;
        }
        return p;
    }));
  }, []);

  const addProject = useCallback(async (name: string, description: string, files: ProjectFile[], isFromPromptGenerator = false): Promise<string | undefined> => {
    setIsLoading(true);
    const newProjectId = uuidv4();
    const currentLang = i18n.language;
    const now = new Date().toISOString();
    
    let projectSpecificFiles = [...files]; 

    if (isFromPromptGenerator && description) {
        let appNameFromPrompt = "Generated_App"; 
        const namePatterns = [
            /^\*\*Application Name:\*\*\s*([^\n]+)/im, /^\*\*Názov aplikácie:\*\*\s*([^\n]+)/im,
            /^\*\*Project Title:\*\*\s*([^\n]+)/im, /^\*\*Názov Projektu:\*\*\s*([^\n]+)/im,
            /^Application Name:\s*([^\n]+)/im, /^Názov aplikácie:\s*([^\n]+)/im,
            /^Project Title:\s*([^\n]+)/im, /^Názov Projektu:\s*([^\n]+)/im,
        ];

        for (const pattern of namePatterns) {
            const match = description.match(pattern);
            if (match && match[1] && match[1].trim()) {
                appNameFromPrompt = match[1].trim().replace(/[^\w\s-]/gi, '').replace(/\s+/g, '_');
                break;
            }
        }
        if (appNameFromPrompt === "Generated_App" && name) {
             appNameFromPrompt = name.trim().replace(/[^\w\s-]/gi, '').replace(/\s+/g, '_');
        }
        if (!appNameFromPrompt) appNameFromPrompt = "App_Blueprint";

        const markdownFile: ProjectFile = {
          id: uuidv4(),
          name: `${appNameFromPrompt}_Developer_Blueprint.md`,
          type: 'text/markdown',
          size: description.length,
          content: description, 
        };
        projectSpecificFiles = [markdownFile, ...files.filter(f => f.name !== markdownFile.name)];
    }

    let tempProject: Project = {
      id: newProjectId,
      name,
      description, 
      files: projectSpecificFiles,
      createdAt: now,
      updatedAt: now,
      lastInteractionTimestamp: now,
      dataCore: { 
        assistantNotes: [],
        aiDevSpecFormData: {},
        blogPostFormData: {},
        landingPageFormData: {}
      },
      selectedTemplate: ProjectTemplate.STANDARD,
      status: 'analyzing_initial',
      isFavorite: false,
      deepAnalysisSubStage: null,
    };
    setProjects(prev => [...prev, tempProject]);

    try {
      const initialAnalysis: AIInitialAnalysis = await geminiService.getInitialAnalysis(name, description, tempProject.files, currentLang);
      const analysisSummary = initialAnalysis.summary || "";
      const analysisTypeGuess = initialAnalysis.projectTypeGuess || "";
      
      const apiKeyMissingErrorString = t('serviceMessages.apiKeyMissingError', {lng: currentLang});
      const apiQuotaExceededErrorString = t('serviceMessages.apiQuotaExceededErrorTitle', {lng: currentLang});
      const initialAnalysisErrorString = t('serviceMessages.initialAnalysisError', {lng: currentLang});

      let isError = false;
      let errorDetails = "";

      if (analysisTypeGuess.startsWith(apiKeyMissingErrorString) || analysisTypeGuess.startsWith(apiQuotaExceededErrorString) || analysisTypeGuess.startsWith(initialAnalysisErrorString)) {
        isError = true;
        errorDetails = analysisSummary || analysisTypeGuess; 
      }
      
      if (isError) {
        showNotification({
            type: analysisTypeGuess.startsWith(apiQuotaExceededErrorString) ? 'api_quota_exceeded' : 'error',
            title: analysisTypeGuess, 
            message: errorDetails || t('notifications.creationFailedTitle', {lng: currentLang})
        });
      }
      
      updateProjectState(newProjectId, { 
        initialAnalysis, 
        status: isError ? 'error' : 'analyzed_initial',
        errorDetails: isError ? errorDetails : undefined,
        dataCore: {...tempProject.dataCore, projectSummary: initialAnalysis.summary} 
      });
      setIsLoading(false);
      return newProjectId;
    } catch (error) { 
      console.error("Error adding project (orchestration):", error);
      const errorDetails = error instanceof Error ? error.message : String(error);
      updateProjectState(newProjectId, { status: 'error', errorDetails });
      showNotification({ type: 'error', title: t('notifications.creationFailedTitle', {lng: currentLang}), message: errorDetails });
      setIsLoading(false);
      return undefined;
    }
  }, [updateProjectState, showNotification, i18n.language, t]);

  const getProjectById = useCallback((id: string): Project | undefined => {
    return projects.find(p => p.id === id);
  }, [projects]);

  const updateProjectDataCore = useCallback((projectId: string, data: Record<string, any>) => {
    const project = getProjectById(projectId);
    if (project) {
      const updatedDataCore = { 
          ...project.dataCore, 
          ...data, 
          assistantNotes: data.assistantNotes || project.dataCore.assistantNotes || [] 
      };
      updateProjectState(projectId, { dataCore: updatedDataCore, lastInteractionTimestamp: new Date().toISOString() });
    }
  }, [getProjectById, updateProjectState]);

  const runDeepAnalysis = useCallback(async (projectId: string) => {
    const project = getProjectById(projectId);
    if (!project) return;

    const currentLang = i18n.language;
    setIsLoading(true); 

    const setSubStage = (subStage: DeepAnalysisSubStage, messageKey: string) => {
      updateProjectState(projectId, { 
        status: subStage ? `analyzing_deep_${subStage}` as ProjectStatus : 'analyzing_deep',
        deepAnalysisSubStage: subStage,
        detailedStatusMessageKey: messageKey,
        lastInteractionTimestamp: new Date().toISOString()
      });
    };
    
    setSubStage(null, 'projectPage.aiAnalyzing'); 

    try {
      setSubStage('narrative', 'projectPage.aiAnalyzingNarrative');
      const strategicOutputCore: Omit<AIStrategicOutput, 'generatedImage' | 'generatedLogo' | 'generatedIcon' | 'generatedBanner'> = 
        await geminiService.performDeepAnalysis(project, currentLang);
      
      let fullStrategicOutput: AIStrategicOutput = { 
        ...strategicOutputCore, 
        generatedImage: project.strategicOutput?.generatedImage, 
        generatedLogo: project.strategicOutput?.generatedLogo,
        generatedIcon: project.strategicOutput?.generatedIcon,
        generatedBanner: project.strategicOutput?.generatedBanner,
      };

      let analysisStatus: ProjectStatus = 'analyzed_deep';
      let errorDetailsStr: string | undefined = undefined;
      
      const apiKeyErrorString = t('serviceMessages.apiKeyMissingError', {lng: currentLang});
      const quotaErrorString = t('serviceMessages.apiQuotaExceededErrorTitle', {lng: currentLang});
      const deepAnalysisFailedString = t('serviceMessages.deepAnalysisFailedError', {lng: currentLang, error:''}).split(':')[0];


      if (strategicOutputCore.projectPageNarrative.startsWith(apiKeyErrorString) || 
          strategicOutputCore.projectPageNarrative.startsWith(quotaErrorString) ||
          strategicOutputCore.projectPageNarrative.startsWith(deepAnalysisFailedString) ||
          strategicOutputCore.projectPageNarrative.includes("failed") || 
          strategicOutputCore.projectPageNarrative.includes("could not be performed")
          ) {
        analysisStatus = 'error';
        errorDetailsStr = strategicOutputCore.projectPageNarrative;
        showNotification({ 
            type: strategicOutputCore.projectPageNarrative.startsWith(quotaErrorString) ? 'api_quota_exceeded' : 'error', 
            title: t('notifications.generationFailedTitle', {lng: currentLang}), 
            message: errorDetailsStr 
        });
      } else {
         if (!fullStrategicOutput.generatedImage?.url) {
            setSubStage('mainImage', 'projectPage.aiAnalyzingMainImage');
            try {
                const imageResult = await geminiService.generateProjectImage(project, currentLang);
                if (imageResult?.url) fullStrategicOutput.generatedImage = imageResult;
            } catch (imgError: any) {
                console.error("Main image generation failed in deep analysis:", imgError.message);
                showNotification({type: imgError.message.includes(quotaErrorString) ? 'api_quota_exceeded' : 'warning', title: t('notifications.generationFailedTitle', {lng:currentLang}), message: t('notifications.imageGenerationFailed', {asset: t('projectPage.generatedImageTitle', {lng:currentLang}), error: imgError.message, lng:currentLang})});
            }
         }
         if (!fullStrategicOutput.generatedLogo?.url && project.dataCore.logoBrief) { 
            setSubStage('logo', 'projectPage.aiAnalyzingLogo');
            try {
                const logoResult = await geminiService.generateLogoFromBrief(project.dataCore.logoBrief, project, currentLang);
                if (logoResult?.url) fullStrategicOutput.generatedLogo = logoResult;
            } catch (logoError: any) {
                 console.error("Logo generation failed in deep analysis:", logoError.message);
                 showNotification({type: logoError.message.includes(quotaErrorString) ? 'api_quota_exceeded' : 'warning', title: t('notifications.generationFailedTitle', {lng:currentLang}), message: t('notifications.imageGenerationFailed', {asset: t('projectPage.nav.projectLogo', {lng:currentLang}), error: logoError.message, lng:currentLang})});
            }
         } else if (!fullStrategicOutput.generatedLogo?.url) { 
             setSubStage('logo', 'projectPage.aiAnalyzingLogo');
             try {
                const logoResult = await geminiService.generateLogo(project, currentLang);
                if (logoResult?.url) fullStrategicOutput.generatedLogo = logoResult;
            } catch (logoError: any) {
                 console.error("Generic logo generation failed in deep analysis:", logoError.message);
                 showNotification({type: logoError.message.includes(quotaErrorString) ? 'api_quota_exceeded' : 'warning', title: t('notifications.generationFailedTitle', {lng:currentLang}), message: t('notifications.imageGenerationFailed', {asset: t('projectPage.nav.projectLogo', {lng:currentLang}), error: logoError.message, lng:currentLang})});
            }
         }
      }
      
      updateProjectState(projectId, {
        strategicOutput: fullStrategicOutput,
        selectedTemplate: strategicOutputCore.suggestedTemplate || project.selectedTemplate,
        dataCore: {
          ...project.dataCore, 
          projectNarrative: fullStrategicOutput.projectPageNarrative,
          knowledgeGraphData: fullStrategicOutput.knowledgeGraph,
          strategicSuggestions: fullStrategicOutput.strategicSuggestions,
        },
        status: analysisStatus,
        deepAnalysisSubStage: null, 
        detailedStatusMessageKey: undefined,
        errorDetails: errorDetailsStr,
        lastInteractionTimestamp: new Date().toISOString(),
      });
    } catch (error) { 
      console.error("Error during deep analysis orchestration:", error);
      const errorDetails = error instanceof Error ? error.message : String(error);
      updateProjectState(projectId, { 
        status: 'error', 
        errorDetails, 
        deepAnalysisSubStage: null, 
        detailedStatusMessageKey: undefined 
      });
      showNotification({ type: 'error', title: t('notifications.generationFailedTitle', {lng:currentLang}), message: errorDetails });
    } finally {
      setIsLoading(false);
    }
  }, [getProjectById, updateProjectState, showNotification, i18n.language, t]);

  const setSelectedTemplate = useCallback((projectId: string, template: ProjectTemplate) => {
    updateProjectState(projectId, { selectedTemplate: template, lastInteractionTimestamp: new Date().toISOString() });
  }, [updateProjectState]);

  const analyzePortfolio = useCallback(async () => {
    if (projects.length < 1) { 
        setPortfolioAnalysis(null);
        return;
    }
    setIsLoadingPortfolio(true);
    const currentLang = i18n.language;
    try {
        const analysisResult = await geminiService.analyzePortfolioSynergies(projects, currentLang);
        if (analysisResult.conflicts.length > 0 && analysisResult.conflicts[0].projectIds?.length === 0 && analysisResult.conflicts[0].description.startsWith(t('serviceMessages.apiQuotaExceededErrorTitle', {lng:currentLang}))) {
            showNotification({type: 'api_quota_exceeded', title: t('serviceMessages.apiQuotaExceededErrorTitle', {lng:currentLang}), message: analysisResult.conflicts[0].description});
        }
        setPortfolioAnalysis(analysisResult);
    } catch (error) { 
        console.error("Error analyzing portfolio:", error);
        const errorDescription = error instanceof Error ? error.message : String(error);
        setPortfolioAnalysis({ 
            synergies: [], 
            conflicts: [{ projectIds: [], description: t('serviceMessages.portfolioAnalysisAPIFail', { error: errorDescription, lng: currentLang}) }], 
            sharedResources: [] 
        });
         showNotification({ type: 'error', title: t('notifications.generationFailedTitle', {lng:currentLang}), message: errorDescription });
    } finally {
        setIsLoadingPortfolio(false);
    }
  }, [projects, showNotification, i18n.language, t]);

  const executeProjectAssistantPrompt = useCallback(async (projectId: string, promptTemplate: string, userInput: string, contextInput?: string): Promise<string> => {
    setIsLoading(true);
    const currentLang = i18n.language;
    
    let finalPromptText = promptTemplate.replace(/{{userInput}}/gi, userInput);
    if (contextInput) { 
        finalPromptText = finalPromptText.replace(/{{contextInput}}/gi, contextInput);
    }

    try {
      const result = await geminiService.executeAssistantPrompt(finalPromptText, contextInput, currentLang);
      updateProjectState(projectId, { lastInteractionTimestamp: new Date().toISOString() });
       if (result.startsWith(t('serviceMessages.apiQuotaExceededErrorTitle', {lng:currentLang}))) {
          showNotification({type: 'api_quota_exceeded', title: t('serviceMessages.apiQuotaExceededErrorTitle', {lng:currentLang}), message: result});
      }
      return result;
    } catch (error) { 
      console.error("Error executing project assistant prompt:", error);
      const errorMsg = error instanceof Error ? error.message : String(error);
      showNotification({ type: 'error', title: t('notifications.generationFailedTitle', {lng:currentLang}), message: errorMsg });
      return t('serviceMessages.assistantExecutionError', { error: errorMsg, lng: currentLang });
    } finally {
      setIsLoading(false);
    }
  }, [updateProjectState, showNotification, i18n.language, t]);

  const addAssistantNoteToDataCore = useCallback((projectId: string, title: string, content: string, promptUsed: string, contextProvided?: string) => {
    const project = getProjectById(projectId);
    if (project) {
      const newNote: AIAssistantNote = {
        id: uuidv4(),
        title,
        content,
        promptUsed,
        contextProvided,
        createdAt: new Date().toISOString(),
      };
      const updatedNotes = [...(project.dataCore.assistantNotes || []), newNote]; 
      updateProjectDataCore(projectId, { assistantNotes: updatedNotes }); 
    }
  }, [getProjectById, updateProjectDataCore]);

  const toggleFavoriteProject = useCallback((projectId: string) => {
    setProjects(prevProjects =>
      prevProjects.map(p =>
        p.id === projectId ? { ...p, isFavorite: !p.isFavorite, lastInteractionTimestamp: new Date().toISOString() } : p
      )
    );
  }, []);

  const addConceptualFileToProject = useCallback((projectId: string, fileName: string, fileType: string, fileSize: number, fileContent: string) => {
    const project = getProjectById(projectId);
    if (project) {
      const existingFileIndex = project.files.findIndex(f => f.name === fileName);
      let updatedFiles;

      const newOrUpdatedFile: ProjectFile = {
        id: existingFileIndex > -1 ? project.files[existingFileIndex].id : uuidv4(),
        name: fileName,
        type: fileType,
        size: fileSize,
        content: fileContent,
      };

      if (existingFileIndex > -1) {
        updatedFiles = [...project.files];
        updatedFiles[existingFileIndex] = newOrUpdatedFile;
      } else {
        updatedFiles = [...project.files, newOrUpdatedFile];
      }
      updateProjectState(projectId, { files: updatedFiles, lastInteractionTimestamp: new Date().toISOString() });
    }
  }, [getProjectById, updateProjectState]);

  const updateProjectGeneratedImage = useCallback((projectId: string, imageType: 'main' | 'logo' | 'icon' | 'banner', details: GeneratedImageDetails) => {
    const project = getProjectById(projectId);
    if (project) {
      const currentStrategicOutput = project.strategicOutput || {
          projectPageNarrative: '',
          suggestedTemplate: ProjectTemplate.STANDARD,
          knowledgeGraph: { elements: [], relations: [], summary: t('serviceMessages.apiKeyMissingError', {lng:i18n.language}) },
          strategicSuggestions: [],
          suggestedAssistantPrompts: []
      };
      
      let updatedStrategicOutput: AIStrategicOutput = { ...currentStrategicOutput };
      let imageName = `project_${imageType}.jpg`; 
      let imageSize = details.url.length * 0.75; 

      switch(imageType) {
          case 'main':
              updatedStrategicOutput.generatedImage = details;
              imageName = "project_main_image.jpg";
              break;
          case 'logo':
              updatedStrategicOutput.generatedLogo = details;
              imageName = "project_logo.jpg";
              break;
          case 'icon':
              updatedStrategicOutput.generatedIcon = details;
              imageName = "project_icon.jpg"; 
              break;
          case 'banner':
              updatedStrategicOutput.generatedBanner = details;
              imageName = "project_banner.jpg";
              break;
      }
       
       const existingFileIndex = project.files.findIndex(f => f.name === imageName);
       let updatedFiles = [...project.files];
       const imageFileEntry: ProjectFile = {
            id: existingFileIndex > -1 ? updatedFiles[existingFileIndex].id : uuidv4(),
            name: imageName,
            type: 'image/jpeg', 
            size: imageSize,
            content: details.url 
       };
       if (existingFileIndex > -1) {
           updatedFiles[existingFileIndex] = imageFileEntry;
       } else {
           updatedFiles.push(imageFileEntry);
       }

      updateProjectState(projectId, { strategicOutput: updatedStrategicOutput, files: updatedFiles, lastInteractionTimestamp: new Date().toISOString() });
    }
  }, [getProjectById, updateProjectState, i18n.language, t]);

  const saveLogoBriefToDataCore = useCallback((projectId: string, brief: LogoBriefData) => {
    updateProjectDataCore(projectId, { logoBrief: brief });
  }, [updateProjectDataCore]);

  const fetchAIAssistedLogoBrief = useCallback(async (projectId: string): Promise<Partial<LogoBriefData> | null> => {
    const project = getProjectById(projectId);
    const currentLang = i18n.language;
    if (!project) return null;
    setIsLoading(true);
    try {
      const brief = await geminiService.generateAIAssistedLogoBrief(project, currentLang);
      updateProjectState(projectId, { lastInteractionTimestamp: new Date().toISOString() });
      return brief;
    } catch (error: any) {
      console.error("Error fetching AI assisted logo brief:", error);
       showNotification({ 
          type: error.message.includes(t('serviceMessages.apiQuotaExceededErrorTitle', {lng:currentLang})) ? 'api_quota_exceeded' : 'error',
          title: t('notifications.generationFailedTitle', {lng:currentLang}), 
          message: error.message 
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [getProjectById, updateProjectState, showNotification, i18n.language, t]);

  const generateAndSetProjectLogo = useCallback(async (projectId: string, brief: LogoBriefData) => {
    const project = getProjectById(projectId);
    const currentLang = i18n.language;
    if (!project) return;

    setIsLoading(true);
    updateProjectState(projectId, { status: 'analyzing_deep_logo', detailedStatusMessageKey: 'projectPage.aiAnalyzingLogo', deepAnalysisSubStage: 'logo' });
    try {
      const logoDetails = await geminiService.generateLogoFromBrief(brief, project, currentLang);
      if (logoDetails?.url) {
        updateProjectGeneratedImage(projectId, 'logo', logoDetails); 
        updateProjectState(projectId, { status: 'analyzed_deep', detailedStatusMessageKey: undefined, deepAnalysisSubStage: null });
         showNotification({ 
           type: 'success', 
           title: t('notifications.logoGenerationSuccessTitle', {lng:currentLang}), 
           message: t('notifications.logoAppliedDetail', {lng:currentLang, brandName: brief.brandName}) 
         });
      } else {
        const errorMessage = t('notifications.logoGenerationFailed', {lng:currentLang, error: 'No image URL returned'});
        updateProjectState(projectId, { status: 'error', errorDetails: errorMessage, detailedStatusMessageKey: undefined, deepAnalysisSubStage: null });
        showNotification({ type: 'error', title: t('notifications.generationFailedTitle', {lng:currentLang}), message: errorMessage });
      }
    } catch (error: any) {
      console.error("Error generating and setting project logo:", error);
      updateProjectState(projectId, { status: 'error', errorDetails: error.message, detailedStatusMessageKey: undefined, deepAnalysisSubStage: null });
      showNotification({ 
          type: error.message.includes(t('serviceMessages.apiQuotaExceededErrorTitle', {lng:currentLang})) ? 'api_quota_exceeded' : 'error',
          title: t('notifications.generationFailedTitle', {lng:currentLang}), 
          message: error.message 
      });
    } finally {
      setIsLoading(false);
    }
  }, [getProjectById, updateProjectState, updateProjectGeneratedImage, showNotification, i18n.language, t]);

 const generateAndSaveAIDevSpec = useCallback(async (projectId: string, formData: AIDevSpecFormData) => {
    const project = getProjectById(projectId);
    if (!project) return;
    updateProjectState(projectId, { status: 'generating_ai_dev_spec', detailedStatusMessageKey: 'projectPage.generatingAIDevSpec', lastInteractionTimestamp: new Date().toISOString() });
    try {
        const specContent = await geminiService.generateGenericAsset(project, formData, 'AIDeveloperSpec', i18n.language);
        const specFileName = t('projectPage.aiDeveloperSpecFileName', { projectName: project.name.replace(/[^a-z0-9]/gi, '_'), defaultValue: `${project.name}_AI_Developer_Spec.md` });
        addConceptualFileToProject(projectId, specFileName, 'text/markdown', specContent.length, specContent);
        updateProjectState(projectId, { status: 'analyzed_deep', detailedStatusMessageKey: undefined, errorDetails: undefined, dataCore: {...project.dataCore, aiDevSpecFormData: formData }});
        showNotification({ type: 'success', title: t('notifications.generationSuccessTitle'), message: t('notifications.aiDevSpecGenerated', {fileName: specFileName}) });
    } catch (error: any) {
        console.error("Error generating AI Dev Spec:", error);
        updateProjectState(projectId, { status: 'error', errorDetails: error.message });
        showNotification({ type: 'error', title: t('notifications.generationFailedTitle'), message: error.message });
    }
  }, [getProjectById, updateProjectState, addConceptualFileToProject, showNotification, i18n.language, t]);

  const generateAndSaveBlogPost = useCallback(async (projectId: string, formData: BlogPostFormData) => {
    const project = getProjectById(projectId);
    if (!project) return;
    updateProjectState(projectId, { status: 'generating_blog_post', detailedStatusMessageKey: 'projectPage.generatingBlogPost', lastInteractionTimestamp: new Date().toISOString() });
     try {
        const blogContent = await geminiService.generateGenericAsset(project, formData, 'BlogPost', i18n.language);
        const blogFileName = t('projectPage.blogPostFileName', { projectName: project.name.replace(/[^a-z0-9]/gi, '_'), defaultValue: `${project.name}_Blog_Post.md` });
        addConceptualFileToProject(projectId, blogFileName, 'text/markdown', blogContent.length, blogContent);
        updateProjectState(projectId, { status: 'analyzed_deep', detailedStatusMessageKey: undefined, errorDetails: undefined, dataCore: {...project.dataCore, blogPostFormData: formData }});
        showNotification({ type: 'success', title: t('notifications.generationSuccessTitle'), message: t('notifications.blogPostGenerated', {fileName: blogFileName}) });
    } catch (error: any) {
        console.error("Error generating Blog Post:", error);
        updateProjectState(projectId, { status: 'error', errorDetails: error.message });
        showNotification({ type: 'error', title: t('notifications.generationFailedTitle'), message: error.message });
    }
  }, [getProjectById, updateProjectState, addConceptualFileToProject, showNotification, i18n.language, t]);

  const generateAndSaveLandingPage = useCallback(async (projectId: string, formData: LandingPageFormData) => {
    const project = getProjectById(projectId);
    if (!project) return;
    updateProjectState(projectId, { status: 'generating_landing_page_content', detailedStatusMessageKey: 'projectPage.landingPage.generatingButton', lastInteractionTimestamp: new Date().toISOString() });
    try {
        const specContent = await geminiService.generateGenericAsset(project, formData, 'LandingPageContentAndVisualSpec', i18n.language);
        const specFileName = t('projectPage.landingPage.specFileNameDefaultMD', { projectName: project.name.replace(/[^a-z0-9]/gi, '_'), defaultValue: `${project.name}_Landing_Page_Spec.md` });
        addConceptualFileToProject(projectId, specFileName, 'text/markdown', specContent.length, specContent);
        showNotification({ type: 'info', title: t('notifications.specGeneratedTitle'), message: t('notifications.landingPageSpecGenerated', {fileName: specFileName}) });

        updateProjectState(projectId, { status: 'generating_landing_page_visual_spec', detailedStatusMessageKey: 'projectPage.landingPage.generatingHTML', lastInteractionTimestamp: new Date().toISOString() }); 
        const htmlContent = await geminiService.generateLandingPageHTML(specContent, i18n.language); 
        const htmlFileName = t('projectPage.landingPage.fileNameDefaultHTML', { projectName: project.name.replace(/[^a-z0-9]/gi, '_'), defaultValue: `${project.name}_Landing_Page.html` });
        addConceptualFileToProject(projectId, htmlFileName, 'text/html', htmlContent.length, htmlContent);
        
        updateProjectState(projectId, { status: 'analyzed_deep', detailedStatusMessageKey: undefined, errorDetails: undefined, dataCore: {...project.dataCore, landingPageFormData: formData }});
        showNotification({ type: 'success', title: t('notifications.generationSuccessTitle'), message: t('notifications.landingPageGenerated', {fileName: htmlFileName}) });
    } catch (error: any) {
        console.error("Error generating Landing Page:", error);
        updateProjectState(projectId, { status: 'error', errorDetails: error.message });
        showNotification({ type: 'error', title: t('notifications.generationFailedTitle'), message: error.message });
    }
  }, [getProjectById, updateProjectState, addConceptualFileToProject, showNotification, i18n.language, t]);

  const getAIAssistanceForAIDevSpecForm = useCallback(async (project: Project): Promise<Partial<AIDevSpecFormData>> => {
    setIsLoading(true);
    try {
      const suggestions = await geminiService.generateAIAssistedAIDevSpecForm(project, i18n.language);
      showNotification({type: 'success', title: t('notifications.aiAssistedFormFillTitle'), message: t('notifications.aiAssistedFormFillSuccess')});
      return suggestions;
    } catch (error: any) {
      showNotification({type: 'error', title: t('notifications.aiAssistedFormFillErrorTitle'), message: error.message});
      return {};
    } finally {
      setIsLoading(false);
    }
  }, [showNotification, t, i18n.language]);

  const getAIAssistanceForBlogPostForm = useCallback(async (project: Project): Promise<Partial<BlogPostFormData>> => {
    setIsLoading(true);
    try {
      const suggestions = await geminiService.generateAIAssistedBlogPostForm(project, i18n.language);
      showNotification({type: 'success', title: t('notifications.aiAssistedFormFillTitle'), message: t('notifications.aiAssistedFormFillSuccess')});
      return suggestions;
    } catch (error: any) {
      showNotification({type: 'error', title: t('notifications.aiAssistedFormFillErrorTitle'), message: error.message});
      return {};
    } finally {
      setIsLoading(false);
    }
  }, [showNotification, t, i18n.language]);

  const getAIAssistanceForLandingPageForm = useCallback(async (project: Project): Promise<Partial<LandingPageFormData>> => {
    setIsLoading(true);
    try {
      const suggestions = await geminiService.generateAIAssistedLandingPageForm(project, i18n.language);
      showNotification({type: 'success', title: t('notifications.aiAssistedFormFillTitle'), message: t('notifications.aiAssistedFormFillSuccess')});
      return suggestions;
    } catch (error: any) {
      showNotification({type: 'error', title: t('notifications.aiAssistedFormFillErrorTitle'), message: error.message});
      return {};
    } finally {
      setIsLoading(false);
    }
  }, [showNotification, t, i18n.language]);

  // File Management Functions
  const deleteProjectFile = useCallback((projectId: string, fileId: string) => {
    setProjects(prevProjects =>
      prevProjects.map(p =>
        p.id === projectId
          ? { ...p, files: p.files.filter(f => f.id !== fileId), lastInteractionTimestamp: new Date().toISOString() }
          : p
      )
    );
    showNotification({ type: 'success', title: t('notifications.fileDeletedTitle'), message: t('notifications.fileDeletedSuccess') });
  }, [setProjects, showNotification, t]);

  const duplicateProjectFile = useCallback((projectId: string, fileId: string) => {
    const project = getProjectById(projectId);
    const fileToDuplicate = project?.files.find(f => f.id === fileId);
    if (project && fileToDuplicate) {
      const newFileId = uuidv4();
      const nameParts = fileToDuplicate.name.split('.');
      const extension = nameParts.length > 1 ? `.${nameParts.pop()}` : '';
      const baseName = nameParts.join('.');
      const newFileName = `${baseName}_copy${extension}`;

      const newFile: ProjectFile = {
        ...fileToDuplicate,
        id: newFileId,
        name: newFileName,
      };
      addConceptualFileToProject(projectId, newFile.name, newFile.type, newFile.size, newFile.content || '');
      showNotification({ type: 'success', title: t('notifications.fileDuplicatedTitle'), message: t('notifications.fileDuplicatedSuccess', {fileName: newFileName})});
    } else {
      showNotification({ type: 'error', title: t('notifications.defaultTitleError'), message: t('notifications.fileOperationFailed') });
    }
  }, [getProjectById, addConceptualFileToProject, showNotification, t]);

  const updateProjectFileContent = useCallback((projectId: string, fileId: string, newContent: string) => {
    setProjects(prevProjects =>
      prevProjects.map(p => {
        if (p.id === projectId) {
          return {
            ...p,
            files: p.files.map(f =>
              f.id === fileId ? { ...f, content: newContent, size: newContent.length } : f
            ),
            lastInteractionTimestamp: new Date().toISOString()
          };
        }
        return p;
      })
    );
    showNotification({ type: 'success', title: t('notifications.fileUpdatedTitle'), message: t('notifications.fileUpdatedSuccess') });
  }, [setProjects, showNotification, t]);


  const contextValue: ProjectsContextType = { 
    projects, 
    isLoading, 
    portfolioAnalysis, 
    isLoadingPortfolio, 
    addProject, 
    getProjectById, 
    updateProjectDataCore, 
    runDeepAnalysis, 
    setSelectedTemplate, 
    analyzePortfolio,
    executeProjectAssistantPrompt,
    addAssistantNoteToDataCore,
    toggleFavoriteProject,
    addConceptualFileToProject,
    updateProjectGeneratedImage,
    generateAndSetProjectLogo,
    fetchAIAssistedLogoBrief,
    saveLogoBriefToDataCore,
    generateAndSaveAIDevSpec,
    generateAndSaveBlogPost,
    generateAndSaveLandingPage,
    getAIAssistanceForAIDevSpecForm,
    getAIAssistanceForBlogPostForm,
    getAIAssistanceForLandingPageForm,
    deleteProjectFile,
    duplicateProjectFile,
    updateProjectFileContent
  };

  return React.createElement(
    ProjectsContext.Provider,
    { value: contextValue },
    children
  );
};

export const useProjects = (): ProjectsContextType => {
  const context = useContext(ProjectsContext);
  if (context === undefined) {
    throw new Error('useProjects must be used within a ProjectsProvider');
  }
  return context;
};
