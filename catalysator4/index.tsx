
import React from 'react';
import ReactDOM from 'react-dom/client';
import { HashRouter } from 'react-router-dom';
import App from './src/App'; // Assuming App.tsx is also in src
import { ProjectsProvider } from './src/hooks/useProjects'; // Assuming useProjects.ts is in src/hooks
import { NotificationProvider } from './src/contexts/NotificationContext'; // Added
import { i18nPromise } from './src/i18n.ts'; // Import the promise

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <HashRouter>
      <ProjectsProvider>
        <App />
      </ProjectsProvider>
    </HashRouter>
  </React.StrictMode>
);

async function main() {
  try {
    await i18nPromise; // Wait for i18next to be initialized
    
    // Make sure rootElement exists before using it
    const rootElement = document.getElementById('root');
    if (!rootElement) {
      throw new Error("Could not find root element to mount to");
    }
    
    const root = ReactDOM.createRoot(rootElement);
    root.render(
      <React.StrictMode>
        <HashRouter>
          <NotificationProvider> {/* NotificationProvider now wraps ProjectsProvider */}
            <ProjectsProvider>
              <App />
            </ProjectsProvider>
          </NotificationProvider>
        </HashRouter>
      </React.StrictMode>
    );
  } catch (error) {
    console.error("Failed to initialize i18next or render the application:", error);
    // Get the element again inside the catch block to avoid the null error
    const errorElement = document.getElementById('root');
    if (errorElement) {
      errorElement.innerHTML = 'Error initializing the application. Please try again later.';
    }
  }
}

main();
